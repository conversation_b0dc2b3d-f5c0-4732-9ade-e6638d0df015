import React from "react";
import { Log<PERSON>ox, StatusBar, StyleSheet } from "react-native";
import Navigation from "./src/navigation";
import { COLORS, FONTS } from "./src/themes";
import { UserProvider } from "./src/Hooks/UseContext";
import StoreContext from "./src/redux/config/StoreContext";
import Toast, {
  ErrorToast,
  SuccessToast,
  ToastConfigParams,
  ToastConfig,
  ToastProps,
} from "react-native-toast-message";
import { StripeProvider } from "@stripe/stripe-react-native";
import { PUBLISHABLE_KEY } from "@env";

const App = () => {
  const toastConfig: ToastConfig = {
    error: (props: ToastConfigParams<ToastProps>) => (
      <ErrorToast
        {...props}
        style={styles.errorToastContainer}
        text1Style={[styles.text1, { color: "red" }]}
        text2Style={[styles.text2, { color: "red" }]}
      />
    ),
    success: (props: ToastConfigParams<ToastProps>) => (
      <SuccessToast
        {...props}
        style={styles.successToastContainer}
        text1Style={styles.text1}
        text2Style={styles.text2}
      />
    ),
  };
  // TODO fix this firebse waring later
  LogBox.ignoreLogs(["This method is deprecated", "Please see migration guide for more details"]);

  return (
    <StripeProvider
      publishableKey={"pk_test_2OmsdEgmFShwRneh0j5ZHLIH"}
      merchantIdentifier="merchant.identifier"
      urlScheme="your-url-scheme"
    >
      <StoreContext>
        <UserProvider>
          <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />
          <Navigation />
          <Toast config={toastConfig} />
        </UserProvider>
      </StoreContext>
    </StripeProvider>
  );
};

export default App;
const styles = StyleSheet.create({
  successToastContainer: {
    borderLeftColor: "green",
    marginHorizontal: 15,
    width: "88%",
    borderLeftWidth: 9,
  },
  errorToastContainer: {
    borderLeftColor: COLORS.red,
    marginHorizontal: 15,
    width: "88%",
    borderLeftWidth: 9,
  },
  text1: {
    fontSize: 16,
    fontFamily: FONTS.Nunito_SemiBold,
    color: "green",
  },
  text2: {
    fontSize: 14,
    fontFamily: FONTS.Nunito_Medium,
    color: "green",
  },
});
