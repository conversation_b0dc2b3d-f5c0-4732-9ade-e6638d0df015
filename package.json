{"name": "OW", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.18.1", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^7.0.1", "@react-native-community/picker": "^1.8.1", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/firestore": "^21.6.1", "@react-native-firebase/messaging": "^21.6.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@reduxjs/toolkit": "^2.3.0", "@stripe/stripe-react-native": "^0.41.0", "@twotalltotems/react-native-otp-input": "^1.3.11", "@zegocloud/zego-uikit-prebuilt-call-rn": "^6.3.2", "@zegocloud/zego-uikit-rn": "^2.17.2", "axios": "^1.7.7", "formik": "^2.4.6", "moment": "^2.30.1", "react": "18.2.0", "react-delegate-component": "^1.0.0", "react-native": "0.71.6", "react-native-app-intro-slider": "^4.0.4", "react-native-calendar-strip": "^2.2.6", "react-native-calendars": "^1.1297.0", "react-native-chart-kit": "^6.12.0", "react-native-circular-progress-indicator": "^4.4.2", "react-native-device-info": "^14.0.4", "react-native-dimension": "^1.0.6", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-get-random-values": "^1.11.0", "react-native-gifted-charts": "^1.4.56", "react-native-gifted-chat": "^2.0.1", "react-native-google-places-autocomplete": "2.5.6", "react-native-image-crop-picker": "^0.41.4", "react-native-keep-awake": "4.0.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.16.8", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "2.12.2", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "18.0.0", "react-native-picker-select": "^8.0.4", "react-native-reanimated": "3.5.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "3.29.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-splash-screen": "^3.3.0", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "^13.9.0", "react-native-svg-transformer": "^1.0.0", "react-native-system-navigation-bar": "^2.5.0", "react-native-toast-message": "^2.2.1", "react-native-video": "6.0.0", "react-native-webview": "^13.13.5", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "toggle-switch-react-native": "^3.3.0", "yup": "^1.4.0", "zego-express-engine-reactnative": "^3.19.0", "zego-zim-react-native": "^2.19.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.13", "@types/react": "^18.3.11", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.9", "prettier": "^2.4.1", "react-native-dotenv": "^3.4.11", "react-test-renderer": "18.2.0", "typescript": "^5.6.2"}, "jest": {"preset": "react-native"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}