diff --git a/node_modules/react-native-video/android/src/main/java/com/brentvatne/exoplayer/ReactExoplayerView.java b/node_modules/react-native-video/android/src/main/java/com/brentvatne/exoplayer/ReactExoplayerView.java
index cc72993..4426879 100644
--- a/node_modules/react-native-video/android/src/main/java/com/brentvatne/exoplayer/ReactExoplayerView.java
+++ b/node_modules/react-native-video/android/src/main/java/com/brentvatne/exoplayer/ReactExoplayerView.java
@@ -539,12 +539,21 @@ public class ReactExoplayerView extends FrameLayout implements
 
         builder.setSingleChoiceItems(speedOptions, selectedSpeedIndex, (dialog, which) -> {
             selectedSpeedIndex = which;
-            float speed = switch (which) {
-                case 0 -> 0.5f;
-                case 2 -> 1.5f;
-                case 3 -> 2.0f;
-                default -> 1.0f;
-            };
+            float speed;
+           switch (which) {
+               case 0:
+                   speed = 0.5f;
+                   break;
+               case 2:
+                   speed = 1.5f;
+                   break;
+               case 3:
+                   speed = 2.0f;
+                   break;
+               default:
+                   speed = 1.0f;
+                   break;
+           }
             setRateModifier(speed);
         });
         builder.show();
