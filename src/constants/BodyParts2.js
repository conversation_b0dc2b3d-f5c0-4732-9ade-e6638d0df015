import {getCategoriesForBodyParts2} from '../Services/CategoriesService';

// Dynamic function to get body parts from Firebase
export const getBodyParts2 = async () => {
  try {
    return await getCategoriesForBodyParts2();
  } catch (error) {
    console.error("Error fetching body parts:", error);
   return [];
  }
};

// // Keep static export for backward compatibility (will be deprecated)
// export const BodyParts2 = [
//   {id: 1, label: 'Neck', isSelected: false},
//   {id: 2, label: 'Sciatica', isSelected: false},
//   {id: 3, label: 'Shoulder', isSelected: false},
//   {id: 4, label: 'Back Pain', isSelected: false},
//   {id: 5, label: 'Hip', isSelected: false},
//   {id: 6, label: 'Elbow', isSelected: false},
//   {id: 7, label: 'Wrist & Hand', isSelected: false},
//   {id: 8, label: 'Knee', isSelected: false},
//   {id: 9, label: 'Cervical', isSelected: false},
//   {id: 10, label: '<PERSON> & Ankle', isSelected: false},
//   {id: 11, label: 'Groin', isSelected: false},
//   {id: 12, label: 'Lumbar', isSelected: false},
//   {id: 13, label: 'Thoracic', isSelected: false},
// ];
