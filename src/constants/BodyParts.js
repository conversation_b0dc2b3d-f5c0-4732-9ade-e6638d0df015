import {
  BlueCircle,
  NeckLine,
  ShoulderLine,
  CircleWhite,
  Line,
  CircleRed,
  CircleRed1,
  Line1,
  Line2,
} from '../assets/svgicons';

export const FrontBodyParts = [
  {
    isOpened: false,
    selected: {
      key: 'Neck',
      label: 'Neck',
      line: NeckLine,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 0,
      right: 136,
      marginRight: -55,
      width: 46,
      height: 24,
    },
    notSelected: {
      key: 'Neck',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 52,
      right: 187,
    },
  },
  {
    isOpened: false,

    selected: {
      key: 'shoulder',
      label: 'Shoulder',
      line: ShoulderLine,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 60,
      right: 80,
      marginRight: -50,
      width: 66,
      height: 24,
    },
    notSelected: {
      key: 'shoulder',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 112,
      right: 130,
      width: 50,
    },
  },
  {
    isOpened: false,

    selected: {
      key: 'wrist&Hand',
      label: 'Wrist & Handow',
      line: ShoulderLine,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 135,
      right: 80,
      marginRight: -50,
      width: 106,
      height: 24,
    },
    notSelected: {
      key: 'wrist&Hand',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 186,
      right: 130,
    },
  },
  {
    isOpened: false,

    selected: {
      key: 'elbow',
      label: 'Elbow',
      line: ShoulderLine,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 198,
      right: 70,
      marginRight: -40,
      width: 56,
      height: 24,
    },
    notSelected: {
      key: 'elbow',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 250,
      right: 120,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'groin',
      label: 'Groin',
      line: Line,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 192,
      right: 210,
      marginRight: 50,
      width: 48,
      height: 24,
    },
    notSelected: {
      key: 'groin',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 244,
      right: 210,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'knee',
      label: 'Knee',
      line: Line,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 296,
      right: 205,
      marginRight: 50,
      width: 48,
      height: 24,
    },
    notSelected: {
      key: 'knee',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 348,
      right: 205,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'foot',
      label: 'Foot & Ankle',
      line: Line,
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 400,
      right: 200,
      marginRight: 50,
      width: 88,
      height: 24,
    },
    notSelected: {
      key: 'foot',
      cicle: BlueCircle,
      cicle1: CircleWhite,
      top: 450,
      right: 200,
    },
  },
];

export const BackBodyParts = [
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'cervical',
      label: 'Cervical',
      discription: '(Neck pain)',
      line: Line2,
      cicle1: CircleRed,
      top: 8,
      right: 185,
      marginRight: 30,
      width: 94,
      height: 36,
    },
    notSelected: {
      key: 'cervical',
      cicle: CircleRed1,
      top: 72,
      right: 185,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'thoracic',
      label: 'Thoracic',
      discription: '(back pain)',
      line: Line2,
      cicle1: CircleRed,
      top: 76,
      right: 185,
      marginRight: 50,
      width: 94,
      height: 36,
    },
    notSelected: {
      key: 'thoracic',
      cicle: CircleRed1,
      top: 140,
      right: 185,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'lumbar',
      label: 'Lumbar',
      discription: '(back pain)',
      line: Line2,
      cicle1: CircleRed,
      top: 138,
      right: 185,
      marginRight: 50,
      width: 90,
      height: 36,
    },
    notSelected: {
      key: 'lumbar',
      cicle: CircleRed1,
      top: 202,
      right: 185,
    },
  },
  {
    isOpened: false,
    isOnLeftSide: true,
    selected: {
      key: 'hip',
      label: 'Hip',
      line: Line2,
      cicle1: CircleRed,
      top: 220,
      right: 210,
      marginRight: 65,
      width: 50,
      height: 24,
    },
    notSelected: {
      key: 'hip',
      cicle: CircleRed1,
      top: 270,
      right: 210,
    },
  },
];
