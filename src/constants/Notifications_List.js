/* eslint-disable react/react-in-jsx-scope */
import {Calendar, Check, Clock, Clock1, Star1} from '../assets/svgicons';

export const Notifications_List = [
  {
    id: 1,
    icon: <Check />,
    color: '#79C9FF',
    decription:
      'Scavenger list has been uploaded. Just go and complete the tasks as...',
    time: '5 mins ago',
  },
  {
    id: 2,
    icon: <Clock />,
    color: '#FFBBC7',
    decription: 'Scavenger list will be sent out at 12 pm today.',
    time: '3 hours ago',
  },
  {
    id: 3,
    icon: <Clock1 />,
    color: '#FF6C7C',
    decription: 'Scavenger list will be sent out at 12 pm today.',
    time: '3 hours ago',
  },
  {
    id: 4,
    icon: <Calendar />,
    color: '#FF6C7C',
    decription: 'New Event date has been announced. Please check...',
    time: '3 hours ago',
  },
  {
    id: 5,
    icon: <Star1 width={20} height={20} />,
    color: '#FF6C7C',
    decription: 'Your progress is updated now. Go and check it out.',
    time: '05/02/23',
  },
  {
    id: 6,
    icon: <Check />,
    color: '#79C9FF',
    decription:
      'Scavenger list has been uploaded. Just go and complete the tasks as...',
    time: '5 mins ago',
  },
  {
    id: 7,
    icon: <Clock />,
    color: '#FFBBC7',
    decription: 'Scavenger list will be sent out at 12 pm today.',
    time: '3 hours ago',
  },
  {
    id: 8,
    icon: <Clock1 />,
    color: '#FF6C7C',
    decription: 'Scavenger list will be sent out at 12 pm today.',
    time: '3 hours ago',
  },
];
