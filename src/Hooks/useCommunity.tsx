import {useState} from 'react';
import firestore from '@react-native-firebase/firestore';
import {IMessage, GiftedChat} from 'react-native-gifted-chat';
import {useAppSelector} from '../redux/config/Store';
import {ICommunity} from '../models/ICummunity';

const useCommunity = () => {
  const userId = useAppSelector(state => state.auth.userId);
  const [communities, setCommunities] = useState<ICommunity[]>([]);
  const [loadingCommunities, setLoadingCommunities] = useState(false);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [messageLoading, setMessageLoading] = useState(true);

  // Fetch communities in real-time
  const getCommunities = () => {
    setLoadingCommunities(true);
    const unsubscribe = firestore()
      .collection('Communities')
      .onSnapshot(
        snapshot => {
          const communityList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...(doc.data() as ICommunity),
          }));
          setCommunities(communityList);
          setLoadingCommunities(false);
        },
        err => {
          console.error('Error fetching communities: ', err);
          setLoadingCommunities(false);
        },
      );

    return unsubscribe;
  };

  const sendCommunityMessage = async (
    communityId: string,
    messages: IMessage[],
  ) => {
    setMessages(previousMessages =>
      GiftedChat.append(previousMessages, messages),
    );
    try {
      const messageRef = firestore()
        .collection('Communities')
        .doc(communityId)
        .collection('messages');

      const newMessage = {
        ...messages[0],
        sendBy: userId,
        updatedAt: firestore.FieldValue.serverTimestamp(),
      };

      await messageRef.add(newMessage);
      console.log('Message sent successfully');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const getCommunityMessages = (communityId: string) => {
    try {
      const messagesRef = firestore()
        .collection('Communities')
        .doc(communityId)
        .collection('messages')
        .orderBy('createdAt', 'desc');

      const unsubscribe = messagesRef.onSnapshot(snapshot => {
        const messages = snapshot.docs.map(doc => ({
          _id: doc.id,
          text: doc.data().text || '', // Ensure text is provided
          createdAt: doc.data().createdAt?.toDate() || new Date(), // Convert Firestore timestamp to Date
          user: doc.data().user || {}, // Ensure user object is provided
        }));

        setMessages(messages as IMessage[]);
        setTimeout(() => {
          setMessageLoading(false);
        }, 500);
      });

      return unsubscribe; // Call this function to stop listening when the component unmounts
    } catch (error) {
      console.error('Error fetching messages:', error);
      setMessageLoading(false);
      return () => {}; // Return empty function if error occurs
    }
  };

  const joinCommunity = async (communityId: string, userId: string) => {
    try {
      const communityRef = firestore()
        .collection('Communities')
        .doc(communityId);

      await communityRef.update({
        currentMembers: firestore.FieldValue.arrayUnion(userId), // Add user to community
      });

      console.log(`User ${userId} joined community ${communityId}`);
    } catch (error) {
      console.error('Error joining community:', error);
    }
  };

  const leaveCommunity = async (communityId: string, userId: string) => {
    try {
      const communityRef = firestore()
        .collection('Communities')
        .doc(communityId);

      await communityRef.update({
        currentMembers: firestore.FieldValue.arrayRemove(userId), // Remove user from community
      });

      console.log(`User ${userId} left community ${communityId}`);
    } catch (error) {
      console.error('Error leaving community:', error);
    }
  };

  return {
    communities,
    sendCommunityMessage,
    getCommunityMessages,
    loadingCommunities,
    getCommunities,
    joinCommunity,
    leaveCommunity,
    messages,
    messageLoading,
  };
};

export default useCommunity;
