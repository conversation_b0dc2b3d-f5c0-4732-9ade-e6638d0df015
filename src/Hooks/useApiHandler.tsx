import axios, {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
export default () => {
  //   function handleAxiosErrors(error: AxiosError) {
  //     // INTERNET NOT WORKING
  //     if (error.code === 'ERR_NETWORK') {
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Error',
  //         text2: 'Make sure your internet is working.',
  //       });
  //       return;
  //     }
  //     // GENERAL ERROR
  //     else {
  //       Toast.show({
  //         type: 'error',
  //         text1: 'Error',
  //         text2: 'Something went wrong.',
  //       });
  //     }
  //   }
  function handleApiErrors(error: any) {
    console.log('errror ------- on handler side', error);
    // console.log('(axios.isAxiosError(error)-----', axios.isAxiosError(error));

    if (axios.isAxiosError(error)) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: mapErrorToMessage(error),
      });
      return;
    }
    // INTERNET NOT WORKING
    if (error.code === 'ERR_NETWORK') {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Make sure your internet is working.',
      });
      return;
    }
    // GENERAL ERROR
    else {
      showErrorToast('Something went wrong.');
    }
  }
  const showErrorToast = (message?: string) => {
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: message,
    });
  };
  function mapErrorToMessage(error: AxiosError<any>) {
    const data = error?.response?.data;
    // console.log('format data------', data);
    // console.log('check if condition', typeof data.message);
    // if (typeof data === 'string') {
    //   return data;
    // }
    if (data?.meta) {
      return data?.meta?.message;
    }
    // if (typeof data === 'object') {
    //   if (typeof data?.message === 'string') return data?.message;
    //   if (Array.isArray(data?.message)) return data.message[0];
    // }
    return 'Something went wrong...';
  }
  return {
    showErrorToast,
    mapErrorToMessage,
    handleApiErrors,
  };
};
