import { useEffect } from "react";
import { getLoggedInUser } from "../../redux/slices/AuthSlice";
import { useAppDispatch, useAppSelector } from "../../redux/config/Store";

export const useAuthUser = () => {
  const { user, userId } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (userId) {
      console.log("user.id >>", userId);
      dispatch(getLoggedInUser(userId));
    }
  }, [userId]);
};
