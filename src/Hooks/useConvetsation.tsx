import {useState} from 'react';
import {IChat} from '../models/IChat';
import {useAppSelector} from '../redux/config/Store';
import {IMessage, GiftedChat} from 'react-native-gifted-chat';
import firestore from '@react-native-firebase/firestore';

export default () => {
  const {userId} = useAppSelector(state => state.auth);

  const [messages, setMessages] = useState<IChat[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversations, setConversations] = useState<IChat[]>([]);
  const [conversationsLoading, setConversationsLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(true);

  const onSend = (messages: IChat[], recipientId: string) => {
    const msg = messages[0];

    const myMessage = {
      ...msg,
      sentBy: userId,
      sentTo: recipientId,
      createdAt: new Date(),
      read: false,
      currentMembers: [userId, recipientId],
    };
    console.log('-----', msg);

    setMessages((previousMessages: IChat) =>
      GiftedChat.append(previousMessages, myMessage),
    );
    const uid =
      userId && recipientId > userId
        ? userId + '-' + recipientId
        : recipientId + '-' + userId;

    const convPayload = {
      lastMessage: myMessage,
      currentMembers: [userId, recipientId],
    };

    firestore()
      .collection('Conversations')
      .doc(uid)
      .set(convPayload)
      .then(() => {
        firestore()
          .collection('Conversations')
          .doc(uid)
          .collection('messages')
          .add(myMessage);
      });
  };

  const getAllMessages = async (recipientId: string) => {
    const uid =
      userId && recipientId > userId
        ? userId + '-' + recipientId
        : recipientId + '-' + userId;
    const query = firestore()
      .collection('Conversations')
      .doc(uid)
      .collection('messages')
      .where('currentMembers', 'array-contains', userId)
      .orderBy('createdAt', 'desc');

    query.onSnapshot(
      snapShot => {
        const allMsg = snapShot.docs.map(docSnap => {
          return {
            ...docSnap.data(),
            createdAt: docSnap.data().createdAt.toDate(),
          } as IChat;
        });
        setMessages(allMsg);
        setTimeout(() => {
          setMessagesLoading(false);
        }, 400);
      },
      error => {
        console.log('Error on fetching meesssaages:', error);
        setMessagesLoading(false);
      },
    );
  };

  const getConversations = async () => {
    try {
      const query = firestore()
        .collection('Conversations')
        .where('currentMembers', 'array-contains', userId);

      query.onSnapshot(
        snapShot => {
          const chats: IChat[] = snapShot.docs.reduce(
            (accumulator: IChat[], chat) => {
              accumulator.push({...chat.data(), key: chat.id} as IChat);
              return accumulator;
            },
            [],
          );
          setConversations(chats);
          setIsLoading(false);
        },
        error => {
          console.error('Error in getConversations:', error);
        },
      );
    } catch (error) {
      console.error('Error in getConversations:', error);
      setTimeout(() => {
        setConversationsLoading(false);
      }, 1000);
      // Handle the error appropriately (e.g., show an error message to the user)
    } finally {
      setTimeout(() => {
        setConversationsLoading(false);
      }, 1000);
    }
  };

  return {
    onSend,
    getAllMessages,
    isLoading,
    messages,
    getConversations,
    conversations,
    conversationsLoading,
    messagesLoading,
  };
};
