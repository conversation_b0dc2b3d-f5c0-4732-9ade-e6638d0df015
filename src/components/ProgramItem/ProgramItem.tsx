import {
  ActivityIndicator,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';
import TextButtonwithIcon from '../TextButtonwithIcon';
import {Play, PlayIcon, Plus, Time, Video1} from '../../assets/svgicons';
import ButtonwithIcon from '../ButtonwithIcon';
import {PlayIconStroke} from '../../assets/images';
import CircularProgress from 'react-native-circular-progress-indicator';

interface ProgramProps {
  containerStyle?: ViewStyle;
  showButton: boolean;
  showPercentage?: boolean;
  onPressProgramItem: () => void;
  imageUrl: string;
  duration: number;
  numberofExercises: number;
  name: string;
  type: string;
  onPressAddProgram?: () => void;
  isMyCourse: boolean;
  addNewProgramLoading?: boolean;
  percentage?: number;
}

const ProgramItem: React.FC<ProgramProps> = ({
  containerStyle,
  showButton,
  showPercentage,
  onPressProgramItem,
  imageUrl,
  duration,
  numberofExercises,
  name,
  type,
  onPressAddProgram,
  isMyCourse,
  addNewProgramLoading,
  percentage,
}) => {
  const convertSecondsToTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const formattedTime = [
      String(hours).padStart(2, '0'),
      String(minutes).padStart(2, '0'),
      String(remainingSeconds).padStart(2, '0'),
    ].join(':');

    return formattedTime;
  };
  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={onPressProgramItem}>
      {/* BACKGROUND_IMAGE */}
      <ImageBackground
        source={{uri: imageUrl}}
        style={{
          width: '100%',
          height: 122,
          borderRadius: 8,
          overflow: 'hidden',
        }}>
        <View style={styles.innerContainer1}>
          {/* BADGE */}
          <View style={styles.badge}>
            <Text style={styles.badgelabel}>{type}</Text>
          </View>
          {showPercentage ? (
            <CircularProgress
              value={percentage}
              radius={20}
              activeStrokeWidth={4}
              inActiveStrokeWidth={4}
              activeStrokeColor={COLORS.black}
              inActiveStrokeColor={'#FFE5DD'}
              duration={1000}
              progressValueColor={COLORS.black}
              maxValue={100}
              progressValueStyle={{
                fontSize: 8,
                fontFamily: FONTS.Nunito_SemiBold,
                color: COLORS.black,
              }}
              circleBackgroundColor={'#FFE5DD'}
              valueSuffix={'%'}
            />
          ) : null}
        </View>
      </ImageBackground>
      <View style={{paddingHorizontal: 13, paddingVertical: 14}}>
        {/* TITLE */}
        <Text style={styles.title}>{name}</Text>
        <View style={styles.innerContainer}>
          {/* TIME */}
          <TextButtonwithIcon
            label={convertSecondsToTime(duration)}
            labelStyle={styles.text}
            leftIcon={<Time />}
            disabled
          />
          {/* EXERCISES */}
          <TextButtonwithIcon
            label={`${numberofExercises.toString()} Exercises`}
            labelStyle={styles.text}
            leftIcon={<Video1 />}
            disabled
            containerStyle={{marginRight: 60}}
          />
          {/* ADD */}
          <TouchableOpacity
            disabled={addNewProgramLoading || isMyCourse}
            onPress={onPressAddProgram}
            style={styles.iconContainer}>
            {addNewProgramLoading ? (
              <ActivityIndicator size={'small'} color={COLORS.white} />
            ) : isMyCourse ? (
              <View
                style={{flexDirection: 'row', alignItems: 'center', gap: 6}}>
                <Image
                  source={PlayIconStroke}
                  style={{width: 14, height: 14}}
                />
                <Text style={styles.playText}>Play</Text>
              </View>
            ) : (
              <Plus />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ProgramItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    borderRadius: 8,
    marginHorizontal: 3,
  },
  iconContainer: {
    backgroundColor: COLORS.red,
    borderRadius: 100 / 2,
    // paddingVertical: 8,
    paddingHorizontal: 12,
    height: 32,
    justifyContent: 'center',
  },
  badge: {
    backgroundColor: COLORS.red,
    height: 26,
    borderRadius: 26 / 2,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgelabel: {
    fontSize: 10,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_SemiBold,
    textTransform: 'capitalize',
  },
  innerContainer: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  innerContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
  },

  text: {
    fontSize: 12,
    marginLeft: 5,
    fontFamily: FONTS.Nunito_Light,
  },
  playText: {
    fontSize: 12,
    fontFamily: FONTS.Nunito_Bold,
    color: COLORS.white,
  },
});
