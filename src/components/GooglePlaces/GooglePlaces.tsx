import {StyleSheet, View} from 'react-native';
import React from 'react';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import {useAppSelector} from '../../redux/config/Store';
import {COLORS} from '../../themes';
import {GOOGLE_MAP_KEY} from '@env';

interface Props {
  onPressAddress: (address: string) => void;
}
const GooglePlaces: React.FC<Props> = ({onPressAddress}) => {
  const {user} = useAppSelector(state => state.auth);

  return (
    <View style={{flex: 1}}>
      <GooglePlacesAutocomplete
        placeholder={user.address ? user?.address : 'Search'}
        // placeholder="Search"
        onPress={(data, details = null) => {
          onPressAddress(data?.description);
        }}
        query={{
          key: GOOGLE_MAP_KEY,
          language: 'en',
        }}
        onFail={error => {
          console.log('Error:', error);
        }}
        styles={{
          //  listView: {position: 'absolute', zIndex: 10, top: 60},
          textInput: styles.textInput,
          description: {color: COLORS.black},
        }}
        textInputProps={{
          placeholderTextColor: '#292D3260',
        }}
      />
    </View>
  );
};

export default GooglePlaces;

const styles = StyleSheet.create({
  textInput: {
    color: COLORS.black,
    borderWidth: 1,
    backgroundColor: '#F9F9F9',
    height: 55,
    borderColor: COLORS.borderColor,
    borderRadius: 10,
  },
});
