import { StyleSheet, Text, TouchableOpacity, View, ImageSourcePropType, ViewStyle } from "react-native";
import React from "react";
import { COLORS, FONTS } from "../../themes";
import TextButtonwithIcon from "../TextButtonwithIcon";
import { NotoStar } from "../../assets/svgicons";
import { IUser } from "../../models";
import FastImage from "react-native-fast-image";

// Define the type for the item prop
interface DoctorItem {
  image: ImageSourcePropType;
  name: string;
  speciality: string;
  experience: string;
}

// Define the props interface for DoctorsCard1
interface DoctorsCard1Props {
  item: IUser;
  containerStyle?: ViewStyle;
  onPress: () => void;
}

const RecommendedDoctorsCard: React.FC<DoctorsCard1Props> = ({ item, containerStyle, onPress }) => {
  const empty_avatar =
    "https://img.freepik.com/premium-vector/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-vector-illustration_561158-3383.jpg?semt=ais_hybrid";
  return (
    <TouchableOpacity onPress={onPress} style={[styles.container, containerStyle]}>
      <FastImage
        source={{
          uri: item?.photo || empty_avatar,
        }}
        style={styles.imageContainer}
      />
      <View style={styles.innerContainer}>
        <View>
          <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
            <Text style={styles.name}>{item.name}</Text>
            {item?.avgRating ? (
              <TextButtonwithIcon
                leftIcon={<NotoStar width={15} height={15} />}
                label={item?.avgRating?.toString()}
                labelStyle={{ marginLeft: 3 }}
              />
            ) : null}
          </View>
          {/* <Text style={styles.text}>{item.workExperience.specialization}</Text> */}
          <Text style={styles.text}>{"Therapist"}</Text>
        </View>
        <View style={styles.footer}>
          <Text style={styles.experinceText}>
            {item?.workExperience?.years ? item.workExperience.years + "+ Years of Exp." : null}
          </Text>
          <View style={styles.bookButton}>
            <Text style={styles.boookButtonText}>Book</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default RecommendedDoctorsCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    padding: 9,
    flexDirection: "row",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,
  },
  imageContainer: {
    width: 100,
    height: 100,
    borderRadius: 10,
    backgroundColor: "lightgray",
  },
  text: {
    fontSize: 10,
    color: "#454545",
    marginTop: 3,
    fontFamily: FONTS.Nunito_Light,
  },
  innerContainer: {
    flex: 1,
    marginLeft: 10,
    marginVertical: 6,
    justifyContent: "space-between",
  },
  bookButton: {
    height: 32,
    backgroundColor: COLORS.primary,
    width: 80,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  boookButtonText: {
    fontSize: 12,
    lineHeight: 14,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Bold,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  experinceText: {
    fontSize: 10,
    color: "#454545",
    fontFamily: FONTS.Nunito_Regular,
  },
  name: {
    fontSize: 16,
    color: "#1B1B1B",
    fontFamily: FONTS.Nunito_SemiBold,
  },
});
