import {
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  GestureResponderEvent,
  Insets,
  Text,
} from 'react-native';
import React, {ReactNode} from 'react';

interface ButtonWithIconProps {
  onPress?: (event: GestureResponderEvent) => void;
  icon: ReactNode;
  containerStyle?: ViewStyle;
  hitSlop?: Insets;
  disabled?: boolean;
}

const ButtonwithIcon: React.FC<ButtonWithIconProps> = ({
  onPress,
  icon,
  containerStyle,
  hitSlop,
  disabled,
}) => {
  return (
    <TouchableOpacity
      disabled={disabled}
      hitSlop={hitSlop || {left: 10, right: 10, bottom: 10, top: 10}}
      onPress={onPress}
      style={[styles.container, containerStyle]}>
      {icon}
    </TouchableOpacity>
  );
};

export default ButtonwithIcon;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
