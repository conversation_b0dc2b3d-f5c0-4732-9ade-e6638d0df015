import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import Modal from "react-native-modal";
import { CheckBox } from "../../assets/svgicons";
import { COLORS, FONTS } from "../../themes";

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  filterString: string;
  setFilterString: (filterString: string) => void;
}

const FilterModal: React.FC<Props> = ({ isModalVisible, setModalVisible, filterString, setFilterString }) => {
  const FILTER_DATA = ["Last Month", "Last 2 Month", "Last 3 Month", "Last 6 Month"];

  return (
    <View>
      <Modal
        backdropOpacity={0.2}
        style={styles.modal}
        animationIn={"fadeIn"}
        animationOut={"fadeOut"}
        useNativeDriverForBackdrop={true}
        backdropTransitionOutTiming={0}
        useNativeDriver={true}
        hideModalContentWhileAnimating={true}
        onBackdropPress={() => setModalVisible(false)}
        isVisible={isModalVisible}
      >
        <View style={styles.modalContent}>
          {FILTER_DATA.map((item, index) => {
            return (
              <TouchableOpacity
                onPress={() => {
                  setFilterString(item);
                  setTimeout(() => {
                    setModalVisible(false);
                  }, 300);
                }}
                activeOpacity={0.6}
                key={index}
                style={[
                  styles.optionContainer,
                  {
                    marginBottom: FILTER_DATA.length - 1 === index ? 0 : 8,
                  },
                ]}
              >
                <Text style={styles.optionText}>{item}</Text>
                {filterString === item && <CheckBox width={20} height={20} />}
              </TouchableOpacity>
            );
          })}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 16,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 14,
  },
  optionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 28,
    // backgroundColor: "red",
    alignItems: "center",
  },
  optionText: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Medium,
    lineHeight: 20,
  },
  checkIcon: {
    width: 12,
    height: 10,
  },
});

export default FilterModal;
