import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS, FONTS} from '../../themes';
import {IChat} from '../../models/IChat';
import {IUser} from '../../models';
import moment from 'moment';
import {useAppSelector} from '../../redux/config/Store';
import AuthServices from '../../Services/AuthServices';
import FastImage from 'react-native-fast-image';

interface ChatHomeProps {
  onPress: (recipientData: IUser) => void;
  item: IChat;
}

const ChatHome: React.FC<ChatHomeProps> = ({onPress, item}) => {
  const {userId} = useAppSelector(state => state.auth);
  const [recipientData, setRecipientData] = useState<IUser>();

  useEffect(() => {
    const fetchRecipientData = async () => {
      const recipientId = item?.currentMembers.find(
        (id: string) => id !== userId,
      );

      if (recipientId) {
        try {
          const user = await getUserById(recipientId);
          setRecipientData(user);
          console.log('user by id', user);
        } catch (error) {
          console.log('Error fetching user:', error);
        }
      }
    };

    fetchRecipientData();
  }, [item]);

  const getUserById = async (userId: string) => {
    console.log('thnk.id >>', userId);
    try {
      const response = await AuthServices.GetUserById(userId);
      return response.data as IUser;
    } catch (error) {
      console.log(error);
      throw error; // Optional: re-throw if needed for further handling
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => recipientData && onPress(recipientData)}>
      {recipientData?.photo ? (
        <FastImage
          source={{uri: recipientData?.photo}}
          style={{width: 50, height: 50, borderRadius: 50}}
        />
      ) : null}
      {!recipientData?.photo ? (
        <View
          style={{
            height: 50,
            width: 50,
            borderRadius: 50,
            backgroundColor: '#f0f0f0',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text style={{color: 'black'}}>
            {recipientData?.name?.split('')[0].toLocaleUpperCase()}
          </Text>
        </View>
      ) : null}
      <View style={styles.textContainer}>
        <View style={styles.header}>
          <Text style={styles.name}>{recipientData?.name}</Text>
          <Text style={styles.message}>
            {moment(item.lastMessage.createdAt.toDate()).calendar({
              sameDay: '[Today]',
              nextDay: '[Tomorrow]',
              nextWeek: 'dddd',
              lastDay: '[Yesterday]',
              lastWeek: '[Last] dddd',
              sameElse: 'DD/MM/YYYY',
            })}
          </Text>
        </View>
        <View style={styles.header}>
          <Text style={styles.message}>{item?.lastMessage?.text}</Text>
          {/* {showUnreadCount(item) ? (
            <Text style={styles.unreadCount}>{showUnreadCount(item)}</Text>
          ) : null} */}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ChatHome;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F9F9F9',
    height: 65,
    alignItems: 'center',
    flexDirection: 'row',
    // marginBottom: 14,
    // marginHorizontal: 20,
    paddingHorizontal: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E8E8E8',
  },
  image: {
    width: 50,
    height: 50,
  },
  textContainer: {
    flex: 1,
    marginLeft: 7,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  name: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  message: {
    fontSize: 12,
    color: '#8A8A8A',
    fontFamily: FONTS.Nunito_Regular,
  },
  unreadCount: {
    fontSize: 12,
    color: '#fff',
    backgroundColor: COLORS.primary,
    fontFamily: FONTS.Nunito_Regular,
    borderRadius: 20,
    height: 20,
    width: 20,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
});
