import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';

interface CommunityCardProps {
  containerStyle?: ViewStyle;
  title: string;
  subTitle: string;
  onPress: () => void;
  onPressJoin: () => void;
  isUserJoined: boolean;
  disableCard: boolean;
}

const CommunityCard: React.FC<CommunityCardProps> = ({
  containerStyle,
  title,
  subTitle,
  onPress,
  isUserJoined,
  onPressJoin,
  disableCard,
}) => {
  return (
    <TouchableOpacity
      disabled={disableCard}
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      <View>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subTitle}>{subTitle}</Text>
      </View>
      <TouchableOpacity
        disabled={isUserJoined} // Directly use the boolean
        hitSlop={16}
        onPress={onPressJoin}>
        <Text style={styles.joinText}>
          {isUserJoined ? 'Continue' : 'Join'}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

export default CommunityCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    height: 70,
    shadowColor: '#000',
    shadowOffset: {
      width: -1,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 11,
    marginHorizontal: 1.5,
    marginBottom: 4,
  },
  title: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  subTitle: {
    fontSize: 10,
    color: '#606060',
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  joinText: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_SemiBold,
  },
});
