import { StyleSheet, Text, View } from "react-native";
import React, { useEffect } from "react";
import { COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import Chart from "../Chart";
import { useAppDispatch, useAppSelector } from "../../redux/config/Store";
import {
  fetchActiveBookingsForEarning,
  fetchCurrentPhaseEarnings,
  fetchOverallEarnings,
  fetchPreviousMonthEarnings,
} from "../../redux/slices/EarningSlice";
import { formatPrice } from "../../utilities/app.utils";
import moment from "moment";

const EarningDetails = ({ onViewDetails }: { onViewDetails: () => void }) => {
  const dispatch = useAppDispatch();
  const { userId } = useAppSelector((state) => state.auth);
  const { loading, results, meta } = useAppSelector((state) => state.earnings.currentPhase);

  const chart_data = results.reduce((acc, item) => {
    const day = moment(item.createdAt).format("DD");
    const existingDay = acc.find((d) => d.label === day);

    if (existingDay) {
      existingDay.value += +formatPrice(item.service.price);
    } else {
      acc.push({
        label: day,
        value: +formatPrice(item.service.price),
      });
    }

    return acc;
  }, [] as { label: string; value: number }[]);

  useEffect(() => {
    if (userId) {
      const payload = {
        doctorId: userId,
        startDate: moment().startOf("month").format("YYYY-MM-DD"),
        endDate: moment().endOf("month").format("YYYY-MM-DD"),
      };
      dispatch(fetchPreviousMonthEarnings(userId));
      dispatch(fetchCurrentPhaseEarnings(payload));
      dispatch(fetchActiveBookingsForEarning(userId));
    }
  }, [userId]);

  return (
    <View>
      <Text style={styles.earningText}>Earnings</Text>
      {!loading ? (
        <>
          <View style={styles.priceContainer}>
            <View>
              <Text style={styles.price}>$ {formatPrice(meta.totalEarnings)}</Text>
              <Text style={styles.monthText}>This Month</Text>
            </View>
            <TextButton label={"Details"} labelStyle={{ color: COLORS.red }} onPress={onViewDetails} />
          </View>

          <View style={styles.line} />
          {chart_data.length ? <Chart data={chart_data} /> : null}
        </>
      ) : null}

      {loading ? (
        <>
          <View style={styles.priceContainer}>
            <View style={{ marginTop: 8 }}>
              <View style={{ width: 80, height: 20, backgroundColor: "#EAEBEB", borderRadius: 4, marginBottom: 8 }} />
              <View style={{ width: 60, height: 16, backgroundColor: "#EAEBEB", borderRadius: 4 }} />
            </View>
            <View style={{ width: 50, height: 18, backgroundColor: "#EAEBEB", borderRadius: 8 }} />
          </View>
          <View style={styles.line} />
          <View style={{ height: 200, backgroundColor: "#EAEBEB", margin: 16, borderRadius: 8 }} />
        </>
      ) : null}
    </View>
  );
};

export default EarningDetails;

const styles = StyleSheet.create({
  earningText: {
    fontSize: 16,
    color: "#030F1C",
    fontFamily: FONTS.Nunito_Bold,
    paddingTop: 30,
    paddingLeft: 16,
  },
  priceContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: 24,
    alignItems: "center",
    paddingHorizontal: 20,
  },
  price: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  monthText: {
    fontSize: 12,
    color: "#D5D6D7",
    fontFamily: FONTS.Nunito_Medium,
  },
  line: {
    height: 1,
    backgroundColor: "#EAEBEB",
    marginVertical: 8,
  },
});
