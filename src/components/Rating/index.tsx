import { StyleSheet, Text, TextInput, TextStyle, View, ViewStyle } from "react-native";
import React from "react";
import { FONTS } from "../../themes";
import AppButton from "../AppButton/AppButton";
import StarRating from "react-native-star-rating-widget";

interface IRating {
  ratingCount: number;
  ratingText: string;
}
interface Props {
  rating: IRating;
  setRating: (val: IRating) => void;
  onPressRatingButton: () => void;
  ratingLoading: boolean;
  title: string;
  containerStyle?: ViewStyle;
  ratingContainer?: ViewStyle;
  titleStyle?: TextStyle;
}

const Rating: React.FC<Props> = ({
  setRating,
  rating,
  onPressRatingButton,
  ratingLoading,
  containerStyle,
  title,
  ratingContainer,
  titleStyle,
}) => {
  return (
    <View style={containerStyle}>
      <Text style={[styles.text, titleStyle]}>{title}</Text>
      <View style={{ marginVertical: 20, ...ratingContainer }}>
        <StarRating
          starSize={22}
          color="#F4B400"
          emptyColor="#292D32"
          rating={rating.ratingCount}
          onChange={(val) => setRating({ ...rating, ratingCount: val })}
          enableHalfStar={false}
        />
      </View>
      <TextInput
        placeholder="Write About Your Experience:"
        placeholderTextColor={"#BCBCBC"}
        style={styles.inputContainer}
        value={rating.ratingText}
        onChangeText={(txt) => setRating({ ...rating, ratingText: txt })}
      />
      <AppButton
        disabled={!rating.ratingCount || !rating.ratingText || ratingLoading}
        isLoading={ratingLoading}
        title="Submit"
        onPress={onPressRatingButton}
        containerStyle={styles.button}
      />
    </View>
  );
};

export default Rating;

const styles = StyleSheet.create({
  text: {
    fontSize: 11,
    color: "#263238",
    fontFamily: FONTS.Nunito_Regular,
  },
  inputContainer: {
    height: 50,
    borderWidth: 1,
    borderColor: "#EB4E1F50",
    paddingHorizontal: 15,
    borderRadius: 8,
    color: "#000",
  },
  button: {
    marginHorizontal: 0,
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    height: 48,
  },
});
