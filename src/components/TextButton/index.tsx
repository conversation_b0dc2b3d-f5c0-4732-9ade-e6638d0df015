import { StyleSheet, Text, TouchableOpacity, ViewStyle, TextStyle, ActivityIndicator } from "react-native";
import React from "react";
import { COLORS, FONTS } from "../../themes";

interface TextButtonProps {
  containerStyle?: ViewStyle;
  label: string;
  labelStyle?: TextStyle;
  onPress?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
}

const TextButton: React.FC<TextButtonProps> = ({
  containerStyle,
  label,
  labelStyle,
  onPress,
  disabled = false,
  isLoading,
}) => {
  const isButtonDisabled = disabled || isLoading;

  return (
    <TouchableOpacity
      disabled={disabled}
      style={[styles.container, containerStyle, isButtonDisabled && { backgroundColor: "#f58c6c" }]}
      onPress={onPress}
    >
      {isLoading ? (
        <ActivityIndicator color={COLORS.primary} style={{ alignSelf: "center" }} />
      ) : (
        <Text style={[styles.label, labelStyle]}>{label}</Text>
      )}
    </TouchableOpacity>
  );
};

export default TextButton;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  label: {
    color: COLORS.white,
    fontSize: 15,
    fontFamily: FONTS.Nunito_Bold,
  },
});
