import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';
import {WhiteCheck} from '../../assets/svgicons';

interface ChooseTimeProps {
  day: string;
  containerStyle?: ViewStyle;
  onToPress: () => void;
  onFromPress: () => void;
  from: string;
  to: string;
  isSelected: boolean;
  onSelect: () => void;
}

const ChooseTime: React.FC<ChooseTimeProps> = ({
  day,
  containerStyle,
  onToPress,
  onFromPress,
  from,
  to,
  isSelected,
  onSelect,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}
        onPress={onSelect}>
        <View
          style={[
            styles.iconContainer,
            {
              backgroundColor: isSelected ? COLORS.primary : undefined,
              borderColor: isSelected ? COLORS.primary : COLORS.black,
            },
          ]}>
          <WhiteCheck style={styles.icon} />
        </View>
        <Text style={styles.label}>{day}</Text>
      </TouchableOpacity>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        {/* TO */}
        <TouchableOpacity
          style={[styles.chooseTime, !isSelected ? {opacity: 0.5} : null]}
          onPress={onFromPress}
          disabled={!isSelected}>
          <Text style={{color: '#606060'}}>{from || ''}</Text>
        </TouchableOpacity>
        {/* FROM */}
        <Text style={{marginHorizontal: 10}}>-</Text>
        <TouchableOpacity
          style={[styles.chooseTime, !isSelected ? {opacity: 0.5} : null]}
          onPress={onToPress}
          disabled={!isSelected}>
          <Text style={{color: '#606060'}}>{to || ''}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ChooseTime;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    padding: 4,
    borderRadius: 4,
    height: 14,
    width: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  icon: {
    // width: 24,
    // height: 24,
  },
  label: {
    fontSize: 13,
    color: '#2B2B2B',
    marginLeft: 9,
    textTransform: 'capitalize',
    flex: 1,
    fontFamily: FONTS.Nunito_Regular,
  },
  chooseTime: {
    width: 70,
    height: 35,
    backgroundColor: '#F8F8F8',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#ECECEC',
    borderRadius: 8,
    fontFamily: FONTS.Nunito_Regular,
  },
});
