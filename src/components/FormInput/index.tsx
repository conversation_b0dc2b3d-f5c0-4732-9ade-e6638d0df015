import React, { useEffect, useRef } from "react";
import { Animated, StyleSheet, Text, TextInput, TextInputProps, TouchableOpacity, View, ViewStyle } from "react-native";
import { COLORS, FONTS } from "../../themes";
import { Eye, EyeOff, Lock } from "../../assets/svgicons";

interface FormInputProps extends TextInputProps {
  containerStyle?: ViewStyle;
  leftIcon?: React.ReactNode;
  righIcon?: React.ReactNode;
  leftIconStyle?: ViewStyle;
  rightIconstyle?: ViewStyle;
  errorMessage?: string;
  isPassword?: boolean;
  onPressRightIcon?: () => void;
}
interface RightIconProps {
  righIcon?: React.ReactNode;
  isPassword?: boolean;
  secureTextEntry?: boolean;
  onPressRightIcon?: () => void;
  rightIconstyle?: ViewStyle;
}

const FormInput: React.FC<FormInputProps> = ({
  placeholder,
  placeholderTextColor,
  onChangeText,
  value,
  secureTextEntry,
  keyboardType,
  containerStyle,
  leftIcon,
  righIcon,
  leftIconStyle,
  rightIconstyle,
  onBlur,
  errorMessage,
  isPassword,
  onPressRightIcon,
  ...rest
}) => {
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (errorMessage) {
      startShakeAnimation();
    }
  }, [errorMessage]);

  const startShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: -10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const RightIcon: React.FC<RightIconProps> = ({
    righIcon,
    isPassword,
    secureTextEntry,
    onPressRightIcon,
    rightIconstyle,
  }) => {
    if (righIcon) {
      return (
        <TouchableOpacity activeOpacity={0.6} onPress={onPressRightIcon} style={rightIconstyle} hitSlop={6}>
          {righIcon}
        </TouchableOpacity>
      );
    }

    if (isPassword) {
      return (
        <TouchableOpacity activeOpacity={0.6} onPress={onPressRightIcon} style={rightIconstyle} hitSlop={6}>
          {secureTextEntry ? <EyeOff width={24} height={20} /> : <Eye width={24} height={20} />}
        </TouchableOpacity>
      );
    }

    return null;
  };
  return (
    <Animated.View style={{ transform: [{ translateX: shakeAnimation }] }}>
      <View style={{ ...styles.container, ...containerStyle }}>
        {leftIcon && <View style={{ ...leftIconStyle, width: 25, marginRight: 10 }}>{leftIcon}</View>}
        <TextInput
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          onChangeText={onChangeText}
          value={value}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          style={{
            color: COLORS.black,
            flex: 1,
            fontSize: 14,
            fontFamily: FONTS.Nunito_Regular,
          }}
          onBlur={onBlur}
          {...rest} // pass remaining TextInputProps
        />
        {/* Use the reusable RightIcon component */}
        {(righIcon || isPassword) && (
          <RightIcon
            righIcon={righIcon}
            isPassword={isPassword}
            secureTextEntry={secureTextEntry}
            onPressRightIcon={onPressRightIcon}
            rightIconstyle={rightIconstyle}
          />
        )}
      </View>
      {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}
    </Animated.View>
  );
};

export default FormInput;

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 10,
    alignItems: "center",
    width: "100%",
    height: 55,
    borderColor: COLORS.borderColor,
    paddingHorizontal: 18,
    backgroundColor: "#F9F9F9",
  },
  errorText: {
    fontSize: 12,
    color: COLORS.red,
    paddingTop: 5,
    paddingLeft: 5,
  },
});
