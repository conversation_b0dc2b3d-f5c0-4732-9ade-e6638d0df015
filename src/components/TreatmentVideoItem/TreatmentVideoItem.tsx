import {
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';
import {Thumbnail} from '../../assets/svgicons';

interface VideoProps {
  onPress: () => void;
  name: string;
  duration: number;
  imageUrl: string;
}

const TreatmentVideoItem: React.FC<VideoProps> = ({
  onPress,
  name,
  duration,
  imageUrl,
}) => {
  const convertSecondsToTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const formattedTime = [
      String(hours).padStart(2, '0'),
      String(minutes).padStart(2, '0'),
      String(remainingSeconds).padStart(2, '0'),
    ].join(':');

    return formattedTime;
  };
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <ImageBackground source={{uri: imageUrl}} style={styles.imageContainer}>
        <View style={styles.overlay} />
        <Thumbnail />
      </ImageBackground>
      <Text style={styles.title}>{name}</Text>
      <Text style={styles.time}>{convertSecondsToTime(duration)}</Text>
    </TouchableOpacity>
  );
};

export default TreatmentVideoItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F7F7F7',
    borderRadius: 10,
    paddingBottom: 16,
    marginHorizontal: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  title: {
    fontSize: 14,
    color: COLORS.black,
    marginTop: 11,
    paddingHorizontal: 14,
    fontFamily: FONTS.Nunito_Bold,
  },
  time: {
    fontSize: 10,
    color: COLORS.black,
    marginTop: 5,
    paddingHorizontal: 14,
    fontFamily: FONTS.Nunito_Regular,
  },
  imageContainer: {
    width: '100%',
    height: 140,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'black',
    opacity: 0.3,
  },
});
