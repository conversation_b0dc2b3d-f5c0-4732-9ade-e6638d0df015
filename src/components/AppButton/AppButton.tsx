import {
  StyleSheet,
  Text,
  TextStyle,
  ViewStyle,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';

interface Props {
  title: string;
  marginTop?: string;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  onPress: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const AppButton: React.FC<Props> = ({
  title,
  containerStyle,
  titleStyle,
  onPress,
  isLoading,
  disabled,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      disabled={disabled}
      onPress={onPress}
      style={[styles.container, containerStyle, disabled && {opacity: 0.6}]}>
      {isLoading ? (
        <ActivityIndicator color={'#F5F5F5'} />
      ) : (
        <Text style={[styles.title, titleStyle]}> {title}</Text>
      )}
    </TouchableOpacity>
  );
};

export default AppButton;

const styles = StyleSheet.create({
  container: {
    height: 52,
    backgroundColor: COLORS.primary,
    marginHorizontal: 30,
    borderRadius: 10,
    marginTop: 38,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 14,
    elevation: 17,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    color: COLORS.white,
    lineHeight: 25,
    fontFamily: FONTS.Nunito_Bold,
  },
});
