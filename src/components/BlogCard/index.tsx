import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../themes';

interface BlogCardProps {
  onPress: () => void;
  thumbnail: string;
  title: string;
  about: string;
  containerStyle?: ViewStyle;
}

const BlogCard: React.FC<BlogCardProps> = ({
  onPress,
  thumbnail,
  title,
  about,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      <Image
        source={{uri: thumbnail}}
        style={{width: '100%', height: 140, borderRadius: 10}}
      />
      <Text style={styles.title}>{title}</Text>
      <Text numberOfLines={3} style={styles.time}>
        {about}
      </Text>
    </TouchableOpacity>
  );
};

export default BlogCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F7F7F7',
    borderRadius: 10,
    marginHorizontal: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 14,
    color: COLORS.black,
    marginTop: 11,
    paddingHorizontal: 14,
    fontFamily: FONTS.Nunito_Bold,
  },
  time: {
    fontSize: 10,
    color: COLORS.black,
    marginTop: 5,
    paddingHorizontal: 14,
  },
});
