import React from "react";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { View, StyleSheet } from "react-native";
import { COLORS } from "../../themes";

const BookingCardSkeleton = ({ showRating = false }) => {
  return (
    <>
      <View style={styles.container}>
        <SkeletonPlaceholder>
          <>
            {/* Header with therapy info and rating */}
            <View style={styles.header}>
              <View>
                {/* Therapy text skeleton */}
                <SkeletonPlaceholder.Item width={160} height={18} borderRadius={4} marginBottom={8} />
                {/* Time skeleton */}
                <SkeletonPlaceholder.Item width={120} height={14} borderRadius={4} />
              </View>
              {showRating && <SkeletonPlaceholder.Item width={40} height={24} borderRadius={12} />}
            </View>

            {/* Horizontal line */}
            <SkeletonPlaceholder.Item height={1} marginVertical={12} />

            {/* Date & Time section */}
            <SkeletonPlaceholder.Item width={80} height={16} borderRadius={4} marginBottom={12} />

            <View>
              <View style={styles.dateTimeRow}>
                <SkeletonPlaceholder.Item width={120} height={16} borderRadius={4} />
                <SkeletonPlaceholder.Item width={4} height={4} borderRadius={2} marginHorizontal={8} />
                <SkeletonPlaceholder.Item width={120} height={16} borderRadius={4} />
              </View>
            </View>

            {/* Footer buttons */}
            <View style={styles.footer}>
              <View style={styles.buttonRow}>
                <SkeletonPlaceholder.Item width={40} height={40} borderRadius={20} />
                <SkeletonPlaceholder.Item width={40} height={40} borderRadius={20} marginLeft={12} />
              </View>
              <SkeletonPlaceholder.Item width={45} height={24} borderRadius={4} />
            </View>
          </>
        </SkeletonPlaceholder>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2.62,
    elevation: 2.2,
    paddingTop: 24,
    paddingBottom: 19,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 3,
    marginHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  dateTimeRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 34,
  },
  buttonRow: {
    flexDirection: "row",
  },
});

export default BookingCardSkeleton;
