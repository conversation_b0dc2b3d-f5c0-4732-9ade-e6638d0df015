import { Image, StyleSheet, Text, View, ViewStyle } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import TextButtonwithIcon from "../TextButtonwithIcon";
import { Call1, NotoStar, VideoCall } from "../../assets/svgicons";
import ButtonwithIcon from "../ButtonwithIcon";
import { IBooking } from "../../models/IBooking";
import UserService from "../../Services/UserService";
import { useAppSelector } from "../../redux/config/Store";
import moment from "moment";
import { Image6 } from "../../assets/images";
import BookingCardSkeleton from "./loading";
import { useFocusEffect } from "@react-navigation/native";
interface ApprovedAppointmentProps {
  showRating: boolean;
  containerStyle?: ViewStyle; // containerStyle is optional
}

interface ApprovedAppointmentProps {
  showRating: boolean;
  containerStyle?: ViewStyle; // containerStyle is optional
  onViewDetails: (bookingId: string) => void;
  onStartSession: (bookingId: string) => void;
}

const ApprovedAppointment: React.FC<ApprovedAppointmentProps> = ({
  showRating,
  containerStyle,
  onViewDetails,
  onStartSession,
}) => {
  const { userId } = useAppSelector((state) => state.auth);
  const [today_booking, setTodayBooking] = useState<null | IBooking>(null);
  const [isFetching, setFetching] = useState(true);

  const isWithinSessionTime = today_booking
    ? (() => {
        const bookingDateTime = moment(
          `${today_booking.bookingDateTime.date} ${today_booking.bookingDateTime.time}`,
          "YYYY-MM-DD HH:mm",
        );
        const sessionEndTime = bookingDateTime.clone().add(1, "hour");
        const now = moment();
        return now.isBetween(bookingDateTime, sessionEndTime, null, "[]");
      })()
    : false;

  async function fetchTodayBooking() {
    await UserService.GetTodayBooking(userId)
      .then(({ data }: { data: IBooking[] }) => {
        const today_booking = data.length ? data[0] : null;
        setTodayBooking(today_booking);
      })
      .catch(() => {
        setTodayBooking(null);
      });
    setFetching(false);
  }

  useFocusEffect(
    React.useCallback(() => {
      fetchTodayBooking();
    }, []),
  );

  if (isFetching) return <BookingCardSkeleton />;

  if (today_booking === null) {
    return (
      <View style={styles.emptyStateContainer}>
        <Image source={Image6} style={{ width: 200, height: 150 }} />
        <Text style={styles.emptyStateText}>Nothing to show here!</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "flex-start",
        }}
      >
        <View>
          {/* THERAPY_TEXT  */}
          <Text style={styles.therapyText}>{today_booking?.service.name}</Text>
          {/* TIME */}
          <Text style={styles.timeText}>1 hour @ {`$ ${today_booking?.service.price}`}</Text>
        </View>
        {showRating ? (
          <TextButtonwithIcon
            leftIcon={<NotoStar width={16} height={16} />}
            label={"4.5"}
            labelStyle={{ fontSize: 14, color: "#4C4C4C", marginLeft: 5 }}
            disabled
          />
        ) : null}
      </View>

      {/* HORIZONTAL_LINES */}
      <View style={styles.horizontalLine} />
      {/* DATE_&_TIME */}
      <Text style={styles.dateText}>Date & Time:</Text>
      <View>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Text style={styles.text}>{moment(today_booking?.bookingDateTime.date).format("MMM DD, dddd")}</Text>
          {/* DOT */}
          <View style={styles.dot} />
          <Text style={styles.text}>{today_booking?.bookingDateTime.time}</Text>
        </View>
      </View>
      <View style={styles.footer}>
        <VideoCall />
        <TextButton
          label={"View"}
          labelStyle={{ color: COLORS.red }}
          containerStyle={{ width: 45 }}
          onPress={() => onViewDetails(today_booking?._id)}
        />
      </View>
    </View>
  );
};

export default ApprovedAppointment;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2.62,
    elevation: 2.2,
    paddingVertical: 19,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  emptyStateContainer: {
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    paddingVertical: 16,
    backgroundColor: COLORS.white,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  therapyText: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  timeText: {
    fontSize: 12,
    color: "#404040",
    paddingTop: 5,
    fontFamily: FONTS.Nunito_Regular,
  },
  horizontalLine: {
    marginVertical: 20,
    borderStyle: "dotted",
    borderWidth: 1,
    borderRadius: 1,
    borderColor: COLORS.black,
  },
  dot: {
    width: 7,
    height: 7,
    backgroundColor: "#D9D9D9",
    borderRadius: 7 / 2,
    marginHorizontal: 4,
  },
  text: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Light,
  },
  dateText: {
    fontSize: 16,
    color: "#333333",
    marginBottom: 4,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  startSessionButton: {
    height: 48,
    backgroundColor: COLORS.red,
    marginTop: 28,
    borderRadius: 8,
  },
  // detailText: {
  //   fontSize: 15,
  //   fontWeight: '500',
  //   color: COLORS.red,
  //   alignSelf: 'center',
  //   paddingTop: 17,
  // },
  footer: {
    paddingTop: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  emptyStateText: {
    fontSize: 14,
    color: "#969696",
    fontFamily: FONTS.Nunito_Regular,
    paddingTop: 12,
  },
});
