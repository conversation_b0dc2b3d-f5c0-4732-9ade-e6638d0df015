import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import React, { useEffect, useState } from "react";
import { appStyles, COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import CalendarStrip from "react-native-calendar-strip";
import moment, { duration, Moment } from "moment";
import { IDaySchedule } from "../../models/User";
import { IBookingDateTime } from "../../models/IBooking";
import AppButton from "../AppButton/AppButton";
import UserService from "../../Services/UserService";
import { AxiosError, AxiosResponse } from "axios";
import { IMeta, IResponse } from "../../models/IResponse";
import { IAvailableSlots } from "../../models/IAvailableSlots";
import { IOfferService } from "../../models/IOfferService";

interface Values {
  date: string;
  time: string;
  day: string;
}

interface AppointmentScheduleProps {
  onPress: (values: Values) => void; // Function to handle button press
  schedule: IDaySchedule[];
  currentSchedule?: IBookingDateTime | undefined;
  doctorId: string;
  selectedAppointment: IOfferService;
  reSchduleBookingLoading?: boolean;
}

const AppointmentSchedule: React.FC<AppointmentScheduleProps> = ({
  onPress,
  schedule,
  currentSchedule,
  doctorId,
  selectedAppointment,
  reSchduleBookingLoading,
}) => {
  const [selectedDayTime, setSelectedDayTime] = useState(currentSchedule?.day || "morning");
  const [availableSlotsTime, setAvailableSlotsTime] = useState<string[]>();

  const [chooseHours, setChooseHours] = useState<string | undefined>(currentSchedule?.time);
  const [seletedDate, setSelectedDate] = useState<Moment>(moment(currentSchedule?.date));

  const [isCalendarLoad, setIsCalendarLoad] = useState(true);
  const [slotLoading, setSlotLoading] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setIsCalendarLoad(false);
    }, 800);
  }, []);

  useEffect(() => {
    const formattedDate = seletedDate.format("YYYY-MM-DD");

    const getAvailableTime = async () => {
      setSlotLoading(true);
      try {
        const resp: AxiosResponse<IAvailableSlots> = await UserService.AvailableSlots(
          doctorId,
          formattedDate,
          60,
          selectedDayTime,
        );

        if (resp.status === 200) {
          setAvailableSlotsTime(resp?.data?.availableSlots);
        }
      } catch (error) {
        const err = error as AxiosError;
        const errResponse = err.response.data as IResponse<IMeta>;
        console.log("Error Response", errResponse.meta.message);
        setAvailableSlotsTime([]);
      } finally {
        setSlotLoading(false);
      }
    };
    getAvailableTime();
  }, [seletedDate, selectedDayTime]);

  useEffect(() => {
    if (currentSchedule) {
      setSelectedDate(moment(currentSchedule.date));
      setSelectedDayTime(currentSchedule.day);
      setChooseHours(currentSchedule.time);
    }
  }, [currentSchedule]);

  return (
    <View style={styles.container}>
      <View style={appStyles.line} />

      <Text style={styles.slot}>Schedule</Text>
      {isCalendarLoad ? (
        <View
          style={{
            height: 130,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <ActivityIndicator size={"small"} />
        </View>
      ) : (
        <CalendarStrip
          selectedDate={seletedDate}
          startingDate={moment()}
          minDate={moment()} // Prevents selecting past dates
          scrollable
          scrollToOnSetSelectedDate
          calendarAnimation={{
            duration: 200,
            type: "parallel",
          }}
          useNativeDriver={true}
          style={{
            paddingBottom: 20,
            marginTop: 10,
            height: 120,
          }}
          calendarHeaderStyle={{
            paddingBottom: 20,
            fontSize: 12,
            fontFamily: FONTS.Nunito_Regular,
            color: "#9E9E9E",
          }}
          onDateSelected={(date) => {
            if (date.isBefore(moment(), "day")) return;
            setSelectedDate(date);
          }}
          showMonth={true}
          maxDayComponentSize={80}
          minDayComponentSize={65}
          dayComponentHeight={76}
          dateNumberStyle={styles.dateNumberText}
          dateNameStyle={styles.dateNameText}
          highlightDateNumberStyle={styles.highlightDateNumberText}
          highlightDateNameStyle={styles.highlightDateNameText}
          highlightDateContainerStyle={{ backgroundColor: COLORS.primary }}
          // leftSelector={[]}
          // rightSelector={[]}
          dayContainerStyle={styles.dayContainer}
          // datesBlacklist={date => date.isoWeek() === 6}
          disabledDateNameStyle={{ opacity: 0.5 }}
          disabledDateNumberStyle={{ opacity: 0.5 }}
          disabledDateOpacity={0}
        />
      )}

      <Text style={styles.slot}>Slot</Text>

      {/* CHOOSE_TIME */}
      <View style={styles.buttonContainer}>
        <TextButton
          label={"Morning"}
          labelStyle={[styles.label, { color: selectedDayTime === "morning" ? COLORS.white : "#9E9E9E" }] as TextStyle}
          containerStyle={
            [
              styles.button,
              {
                backgroundColor: selectedDayTime === "morning" ? COLORS.red : "#F5F6F7",
              },
            ] as ViewStyle
          }
          onPress={() => setSelectedDayTime("morning")}
        />
        <TextButton
          label={"Afternoon"}
          labelStyle={
            [
              styles.label,
              {
                color: selectedDayTime === "afternoon" ? COLORS.white : "#9E9E9E",
              },
            ] as TextStyle
          }
          containerStyle={
            [
              styles.button,
              {
                backgroundColor: selectedDayTime === "afternoon" ? COLORS.red : "#F5F6F7",
              },
            ] as ViewStyle
          }
          onPress={() => setSelectedDayTime("afternoon")}
        />
        <TextButton
          label={"Evening"}
          labelStyle={[styles.label, { color: selectedDayTime === "evening" ? COLORS.white : "#9E9E9E" }] as TextStyle}
          containerStyle={
            [
              styles.button,
              {
                backgroundColor: selectedDayTime === "evening" ? COLORS.red : "#F5F6F7",
              },
            ] as ViewStyle
          }
          onPress={() => setSelectedDayTime("evening")}
        />
      </View>
      {/* AVAILABLE_TIME */}
      <Text style={[styles.slot, { paddingTop: 30 }]}>Available Time</Text>
      <View style={{ height: 34, marginTop: 16 }}>
        {slotLoading ? (
          <ActivityIndicator style={{ height: 34 }} size={"small"} />
        ) : (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ flexDirection: "row" }}
          >
            {availableSlotsTime?.length
              ? availableSlotsTime?.map((item) => {
                  return (
                    <TouchableOpacity
                      onPress={() => setChooseHours(item)}
                      key={item}
                      style={[
                        styles.timeContainer,
                        {
                          backgroundColor: item === chooseHours ? COLORS.red : "#F5F6F7",
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.text,
                          {
                            color: item === chooseHours ? COLORS.white : "#9E9E9E",
                          },
                        ]}
                      >
                        {item}
                      </Text>
                    </TouchableOpacity>
                  );
                })
              : null}
          </ScrollView>
        )}

        {slotLoading
          ? null
          : availableSlotsTime?.length === 0 && (
              <View style={[styles.noSlotAlet]}>
                <Text style={styles.noSlotText}>No time slot available</Text>
              </View>
            )}
      </View>

      {/* CONFIRM_APPOINTMENT */}
      <AppButton
        title="Confirm Appointment"
        disabled={!seletedDate || !selectedDayTime || !chooseHours || reSchduleBookingLoading}
        containerStyle={styles.appointButton}
        onPress={() =>
          onPress({
            date: seletedDate.format("YYYY-MM-DD"),
            day: selectedDayTime,
            time: chooseHours,
          })
        }
        isLoading={reSchduleBookingLoading}
      />
    </View>
  );
};

export default AppointmentSchedule;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    padding: 20,
    paddingBottom: 30,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
  },
  slot: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  label: {
    fontSize: 14,
    color: COLORS.white,
  },
  button: {
    width: "30%",
    backgroundColor: COLORS.red,
    paddingVertical: 13,
    borderRadius: 5,
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 15,
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.red,
    marginRight: 16,
    borderRadius: 5,
    paddingHorizontal: 16,
    height: 34,
  },
  text: {
    fontSize: 12,
  },
  appointButton: {
    height: 50,
    borderRadius: 8,
    marginBottom: 40,
    marginTop: 70,
    backgroundColor: COLORS.red,
    marginHorizontal: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  dateNumberText: {
    fontSize: 12,
    color: "#9E9E9E",
    fontFamily: FONTS.Nunito_Medium,
  },
  dateNameText: {
    color: "#9E9E9E",
    fontSize: 12,
    fontFamily: FONTS.Nunito_Medium,
  },
  highlightDateNumberText: {
    color: COLORS.white,
    fontSize: 12,
    fontFamily: FONTS.Nunito_Medium,
  },
  highlightDateNameText: {
    color: COLORS.white,
    fontSize: 12,
    fontFamily: FONTS.Nunito_Medium,
  },
  dayContainer: {
    backgroundColor: "#C8D2D435",
    borderRadius: 10,
    width: 50,
    height: 76,
  },
  noSlotAlet: {
    backgroundColor: "#F5F6F7",
    paddingHorizontal: 12,
    alignSelf: "center",
    borderRadius: 10,
    height: 34,
    justifyContent: "center",
  },
  noSlotText: { fontSize: 13, fontFamily: FONTS.Nunito_Bold, color: "#9E9E9E" },
});
