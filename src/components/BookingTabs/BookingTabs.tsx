import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import React, {useRef, useEffect} from 'react';
import {COLORS, FONTS} from '../../themes';

interface Props {
  activeTab: string;
  setActiveTab: (val: 'Active' | 'Past') => void;
}

const BookingTabs: React.FC<Props> = ({activeTab, setActiveTab}) => {
  const screenWidth = Dimensions.get('screen').width;
  const tabWidth = screenWidth / 2;

  // Animation logic
  const translateX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    let targetIndex = 0;
    if (activeTab === 'Active') targetIndex = 0;
    else if (activeTab === 'Past') targetIndex = 1;

    Animated.timing(translateX, {
      toValue: targetIndex * tabWidth,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [activeTab, tabWidth]);

  return (
    <View style={styles.tabsContainer}>
      {/* Animated sliding indicator */}
      <Animated.View
        style={[styles.indicator, {width: tabWidth, transform: [{translateX}]}]}
      />
      {['Active', 'Past'].map(tab => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabContainer, {width: tabWidth}]}
          onPress={() => setActiveTab(tab as 'Active' | 'Past')}>
          <Text
            style={[
              styles.tabText,
              {color: activeTab === tab ? COLORS.red : '#515151'},
            ]}>
            {tab}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default BookingTabs;

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 46,
    borderBottomWidth: 3,
    borderBottomColor: '#515151',
    paddingTop: 8,
  },
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    fontSize: Dimensions.get('screen').width / 28,
    fontFamily: FONTS.Nunito_Medium,
  },
  indicator: {
    position: 'absolute',
    height: 3,
    backgroundColor: COLORS.primary,
    bottom: -3,
    zIndex: 200,
  },
});
