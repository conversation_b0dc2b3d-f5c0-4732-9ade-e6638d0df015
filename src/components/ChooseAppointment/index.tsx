import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import React, { useState } from "react";
import { appStyles, COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import { IOfferService } from "../../models/IOfferService";

interface ChooseAppointmentProps {
  onContinuePress: (v: IOfferService) => void;
  services: IOfferService[];
  currentService?: IOfferService;
  setSelectedAppointment: (val: IOfferService) => void;
}

const ChooseAppointment: React.FC<ChooseAppointmentProps> = ({
  onContinuePress,
  services,
  currentService,
  setSelectedAppointment,
}) => {
  const [selectedCard, setSelectedCard] = useState<IOfferService | undefined>(currentService);

  return (
    <View style={styles.container}>
      <View style={appStyles.line} />

      <Text style={styles.chooseText}>Choose Appointment</Text>

      {/* APPOINTMENT_LISTS */}
      {(Array.isArray(services) ? services : [services]).map((item, i) => {
        return (
          <TouchableOpacity
            key={i}
            style={[
              styles.innerContainer,
              {
                borderColor: selectedCard?.name === item.name ? COLORS.red : "#00000007",
              },
            ]}
            onPress={() => {
              setSelectedCard(item);
              setSelectedAppointment(item);
            }}
          >
            <Text style={styles.speciality}>{item.name}</Text>
            <Text style={styles.timeText}>1 hour @ ${Number(item.price).toFixed(2)}</Text>
          </TouchableOpacity>
        );
      })}

      <TextButton
        label={"Continue"}
        disabled={!selectedCard}
        containerStyle={
          [styles.continueButton, { backgroundColor: selectedCard ? COLORS.red : "#EB4E1F80" }] as ViewStyle
        }
        onPress={() => onContinuePress(selectedCard)}
      />
    </View>
  );
};

export default ChooseAppointment;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    paddingVertical: 12,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingBottom: 30,
    paddingHorizontal: 16,
  },

  chooseText: {
    fontSize: 14,
    color: COLORS.black,
    paddingTop: 25,
    marginBottom: 18,
    fontFamily: FONTS.Nunito_Bold,
  },
  innerContainer: {
    backgroundColor: "#00000007",
    paddingVertical: 20,
    paddingLeft: 24,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 10,
  },
  speciality: {
    fontSize: 14,
    color: "#000000",
    fontFamily: FONTS.Nunito_Bold,
  },
  timeText: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Light,
  },
  continueButton: {
    height: 50,
    borderRadius: 8,
    marginBottom: 24,
    marginTop: 40,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
});
