import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import TextButtonwithIcon from "../TextButtonwithIcon";
import { NotoStar } from "../../assets/svgicons";
import { IBooking } from "../../models/IBooking";
import moment from "moment";

interface BookingCardProps {
  startSession?: boolean;
  onPressViewDetails: () => void;
  booking: IBooking;
  onPressSessionStart?: () => void;
  isLoading?: boolean;
}

const ProfessionalBookingCard: React.FC<BookingCardProps> = ({
  onPressViewDetails,
  booking,
  onPressSessionStart,
  isLoading,
}) => {
  const isWithinSessionTime = booking
    ? (() => {
        const bookingDateTime = moment(
          `${booking.bookingDateTime.date} ${booking.bookingDateTime.time}`,
          "YYYY-MM-DD HH:mm",
        );
        const sessionEndTime = bookingDateTime.clone().add(1, "hour");
        const now = moment();
        return now.isBetween(bookingDateTime, sessionEndTime, null, "[]");
      })()
    : false;
  return (
    <TouchableOpacity onPress={onPressViewDetails} style={styles.container}>
      <View style={styles.row}>
        <View>
          <Text style={styles.therapyText}>{booking?.service?.name}</Text>
          <Text style={styles.timeText}>1 hour @ ${booking?.service?.price}</Text>
        </View>
        {booking?.review?.rating ? (
          <TextButtonwithIcon
            leftIcon={<NotoStar width={16} height={16} />}
            label={booking?.review?.rating.toString()}
            labelStyle={styles.ratingLabel}
            disabled
          />
        ) : null}
      </View>

      <View style={styles.horizontalLine} />
      <View>
        <Text style={styles.dateText}>Date & Time:</Text>
        <View style={styles.innerContainer}>
          <View style={styles.dateRow}>
            <Text style={styles.text}>{moment(booking?.bookingDateTime?.date).format("MMM DD, dddd")}</Text>
            <View style={styles.dot} />
            <Text style={styles.text}>
              <Text style={styles.text}>{booking?.bookingDateTime?.time}</Text>
            </Text>
          </View>
        </View>
      </View>
      {booking.status === "active" ? (
        <View>
          <TextButton
            label={"Start a Session"}
            containerStyle={styles.startSessionButton}
            labelStyle={{ color: COLORS.white }}
            onPress={onPressSessionStart}
            disabled={isLoading}
            isLoading={isLoading}
          />
          <Text style={styles.detailText}>View Additional Details</Text>
        </View>
      ) : null}
    </TouchableOpacity>
  );
};

export default ProfessionalBookingCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2.62,
    elevation: 4,
    paddingVertical: 19,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 16,
    marginTop: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  therapyText: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  timeText: {
    fontSize: 12,
    color: "#404040",
    paddingTop: 5,
  },
  horizontalLine: {
    marginVertical: 20,
    borderStyle: "dotted",
    borderWidth: 1,
    borderRadius: 1,
    borderColor: COLORS.black,
  },
  dot: {
    width: 7,
    height: 7,
    backgroundColor: "#D9D9D9",
    borderRadius: 7 / 2,
    marginHorizontal: 4,
  },
  text: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Regular,
  },
  dateText: {
    fontSize: 16,
    color: "#333333",
    marginBottom: 4,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  startSessionButton: {
    height: 48,
    backgroundColor: "#EB4E1F",
    marginTop: 28,
    borderRadius: 8,
  },
  detailText: {
    fontSize: 15,
    color: COLORS.red,
    alignSelf: "center",
    paddingTop: 17,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingLabel: {
    fontSize: 14,
    color: "#4C4C4C",
    marginLeft: 5,
  },
  dateRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  innerContainer: {},
});
