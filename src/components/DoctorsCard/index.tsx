import {
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ImageSourcePropType,
  ViewStyle,
} from "react-native";
import React from "react";
import { Logo } from "../../assets/images";
import { COLORS, FONTS } from "../../themes";
import { IUser } from "../../models";
import FastImage from "react-native-fast-image";
import TextButtonWithIcon from "../TextButtonwithIcon";
import { NotoStar } from "../../assets/svgicons";

interface DoctorItem {
  image: ImageSourcePropType;
  name: string;
  speciality: string;
  recommendation?: boolean;
}

interface DoctorsCardProps {
  item: IUser;
  containerStyle?: ViewStyle;
  onPress: () => void;
}

const DoctorsCard: React.FC<DoctorsCardProps> = ({ item, containerStyle, onPress }) => {
  return (
    <TouchableOpacity onPress={onPress} style={[styles.container, containerStyle]}>
      {/* BACKGROUND_IMAGE */}
      <FastImage resizeMode="cover" source={{ uri: item?.photo }} style={styles.imageContainer}>
        {/* RECOMMENDED */}
        {item && (
          <View style={styles.recomdend}>
            <Image source={Logo} style={{ width: 25, height: 15 }} />
            <Text style={styles.text}>Recommended</Text>
          </View>
        )}
        {/* DOCTOR_ABOUT */}
        <View style={{ flex: 1, justifyContent: "flex-end", margin: 15 }}>
          <View style={styles.drInfo}>
            <View style={{ flex: 1 }}>
              <Text numberOfLines={1} style={styles.name}>
                {item.name}
              </Text>
              <View style={{ flexDirection: "row", alignItems: "center", gap: 6 }}>
                <Text style={styles.specialityText}>{"Therapist"}</Text>
                {item?.avgRating ? (
                  <TextButtonWithIcon
                    leftIcon={<NotoStar width={12} height={12} />}
                    label={item?.avgRating?.toString()}
                    labelStyle={{ marginLeft: 2, fontSize: 12, marginBottom: 1.5 }}
                    containerStyle={{ marginTop: 3 }}
                  />
                ) : null}
              </View>
            </View>
            <View style={styles.bookButton}>
              <Text style={styles.boookButtonText}>Book</Text>
            </View>
          </View>
        </View>
      </FastImage>
    </TouchableOpacity>
  );
};

export default DoctorsCard;

const styles = StyleSheet.create({
  container: {
    width: 250,
    height: 330,
  },
  imageContainer: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    overflow: "hidden",
  },
  recomdend: {
    borderWidth: 1,
    borderColor: COLORS.white,
    padding: 7,
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 6,
    width: 130,
    margin: 15,
    backgroundColor: `${COLORS.black}40`,
  },
  text: {
    fontSize: 12,
    color: "#E7E7E7",
    marginLeft: 6,
    fontFamily: FONTS.Nunito_Bold,
  },
  drInfo: {
    backgroundColor: COLORS.white,
    height: 60,
    borderRadius: 10,
    paddingHorizontal: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_ExtraBold,
    marginRight: 6,
  },
  specialityText: {
    fontSize: 10,
    color: "#4C4C4C",
    fontFamily: FONTS.Nunito_Light,
  },
  bookButton: {
    height: 32,
    backgroundColor: COLORS.primary,
    width: 80,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  boookButtonText: {
    fontSize: 12,
    lineHeight: 14,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Bold,
  },
});
