import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import TextButtonwithIcon from '../TextButtonwithIcon';
import {NotoStar} from '../../assets/svgicons';

interface Props {
  rating: string;
  reviewText: string;
}

const Review: React.FC<Props> = ({rating, reviewText}) => {
  return (
    <View style={styles.card}>
      <Text style={styles.text}>Rate:</Text>
      <TextButtonwithIcon
        label={rating}
        labelStyle={{fontSize: 18, marginLeft: 5}}
        leftIcon={<NotoStar />}
        containerStyle={{marginTop: 20}}
        disabled
      />
      <Text style={styles.paragraph}>{reviewText}</Text>
    </View>
  );
};

export default Review;

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FBFBFB',
    padding: 12,
    marginTop: 16,
  },
  text: {
    fontSize: 11,
    color: '#263238',
  },
  paragraph: {
    fontSize: 11,
    color: '#4C4C4C',
    marginTop: 12,
  },
});
