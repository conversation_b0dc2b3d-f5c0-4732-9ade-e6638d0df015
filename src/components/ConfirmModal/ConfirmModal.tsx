import React from 'react';
import {View, Text, StyleSheet, Platform} from 'react-native';
import Modal from 'react-native-modal';
import TextButton from '../TextButton';
import {COLORS, FONTS} from '../../themes';
import AppButton from '../AppButton/AppButton';

interface ConfirmModalProps {
  isVisible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  loading?: boolean;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isVisible,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = 'Yes, Confirm',
  cancelLabel = 'Cancel',
  loading,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      backdropOpacity={0.2}
      style={styles.modal}>
      <View style={styles.container}>
        {/* TITLE */}
        <Text style={styles.title}>{title}</Text>
        {/* MESSAGE */}
        <Text style={styles.message}>{message}</Text>
        {/* CONFIRM BUTTON */}
        <AppButton
          title={confirmLabel}
          titleStyle={{color: COLORS.white}}
          onPress={onConfirm}
          containerStyle={styles.confirmButton}
          isLoading={loading}
          disabled={loading}
        />

        {/* CANCEL BUTTON */}
        <TextButton
          label={cancelLabel}
          labelStyle={{color: '#A7A7A7'}}
          containerStyle={{marginVertical: 20}}
          onPress={onClose}
        />
      </View>
    </Modal>
  );
};

export default ConfirmModal;

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  container: {
    paddingTop: 30,
    paddingHorizontal: 24,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  title: {
    fontSize: 18,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  message: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  confirmButton: {
    backgroundColor: COLORS.red,
    marginTop: 36,
    height: 42,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
    marginHorizontal: 0,
  },
});
