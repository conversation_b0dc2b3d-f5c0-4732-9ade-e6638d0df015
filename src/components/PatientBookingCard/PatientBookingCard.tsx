import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";
import React from "react";
import { COLORS, FONTS } from "../../themes";
import TextButton from "../TextButton";
import TextButtonwithIcon from "../TextButtonwithIcon";
import { NotoStar } from "../../assets/svgicons";
import { IBooking } from "../../models/IBooking";
import moment from "moment";

interface PatientBookingCardProps {
  containerStyle?: ViewStyle;
  onStartSessionPress?: () => void;
  onCancelPress?: () => void;
  startSession?: boolean;
  showRating?: boolean;
  onPress?: () => void;
  booking: IBooking;
}

const PatientBookingCard: React.FC<PatientBookingCardProps> = ({
  containerStyle,
  onStartSessionPress,
  onCancelPress,
  startSession,
  showRating,
  onPress,
  booking,
}) => {
  const isWithinSessionTime = booking
    ? (() => {
        const bookingDateTime = moment(
          `${booking.bookingDateTime.date} ${booking.bookingDateTime.time}`,
          "YYYY-MM-DD HH:mm",
        );
        const sessionEndTime = bookingDateTime.clone().add(1, "hour");
        const now = moment();
        return now.isBetween(bookingDateTime, sessionEndTime, null, "[]");
      })()
    : false;

  return (
    <TouchableOpacity style={[styles.container, containerStyle]} onPress={onPress}>
      <View style={styles.header}>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <View style={styles.lineStyles} />
          <View>
            <Text style={styles.name}>{booking?.doctor?.name}</Text>
            <Text style={styles.docType}>Therapist</Text>
          </View>
        </View>
        {booking?.review?.rating && booking?.status === "completed" && (
          <TextButtonwithIcon
            leftIcon={<NotoStar width={16} height={16} />}
            label={booking?.review?.rating?.toString()}
            labelStyle={{
              fontSize: 14,
              color: "#4C4C4C",
              marginLeft: 5,
              fontFamily: FONTS.Nunito_Regular,
            }}
            disabled
          />
        )}
      </View>
      <View style={styles.horizontalLine} />
      <View>
        <Text style={styles.dateText}>Date & Time:</Text>
        <View style={styles.innerContainer}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={styles.text}>{moment(booking?.bookingDateTime?.date).format("MMM DD, dddd")}</Text>
            <View style={styles.dot} />
            <Text style={styles.text}>
              <Text style={styles.text}>{booking?.bookingDateTime?.time}</Text>
            </Text>
          </View>
          {/* {booking?.status === 'completed' ? null : (
            <TextButton
              label={'Cancel?'}
              labelStyle={{
                color: COLORS.red,
                fontFamily: FONTS.Nunito_Regular,
                fontSize: 12,
              }}
              onPress={onCancelPress}
            />
          )} */}
        </View>
      </View>
      {startSession ? (
        <TextButton
          label={"Start Session"}
          containerStyle={styles.startSessionButton}
          labelStyle={{ color: COLORS.white }}
          onPress={onStartSessionPress}
        />
      ) : null}
    </TouchableOpacity>
  );
};

export default PatientBookingCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    paddingVertical: 19,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 18,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  lineStyles: {
    height: 33,
    backgroundColor: COLORS.red,
    width: 12,
    marginRight: 5,
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  docType: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Regular,
  },
  horizontalLine: {
    marginVertical: 20,
    borderStyle: "dotted",
    borderWidth: 1,
    borderRadius: 1,
    borderColor: COLORS.black,
  },
  dateText: {
    fontSize: 16,
    color: "#333333",
    marginBottom: 4,
    fontFamily: FONTS.Nunito_Light,
  },
  text: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  dot: {
    width: 7,
    height: 7,
    backgroundColor: "#D9D9D9",
    borderRadius: 7 / 2,
    marginHorizontal: 4,
  },

  innerContainer: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
  },
  startSessionButton: {
    height: 48,
    backgroundColor: COLORS.red,
    marginTop: 28,
    borderRadius: 8,
  },
});
