import AppInstance from '../config/axiosInstance';

async function FetchNotificationByid(userId: string) {
  const response = await AppInstance({
    url: `/notifications/${userId}`,
    method: 'GET',
  });
  return response;
}

async function MarkAllNotificationAsRead(userId: string) {
  const response = await AppInstance({
    url: `/notifications/${userId}`,
    method: 'PATCH',
  });
  return response;
}

const NotificationServices = {
  FetchNotificationByid,
  MarkAllNotificationAsRead
};

export default NotificationServices;
