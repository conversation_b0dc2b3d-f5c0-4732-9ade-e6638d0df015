import { firebase } from "@react-native-firebase/firestore";

export interface ICategory {
  id: string;
  name: string;
}

// Simple function to fetch categories from Firebase
export const fetchCategories = async (): Promise<ICategory[]> => {
  try {
    const snapshot = await firebase.firestore().collection("Categories").get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      name: doc.data().name,
    }));
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
};

// Format categories for UI components (with "All" option)
export const getCategoriesForUI = async (): Promise<Array<{ key: string; label: string }>> => {
  const categories = await fetchCategories();

  const formattedCategories = categories.map((category) => ({
    key: category.name.toLowerCase(),
    label: category.name,
  }));

  return [{ key: "all", label: "All" }, ...formattedCategories];
};

// Format categories for BodyParts2 structure
export const getCategoriesForBodyParts2 = async (): Promise<
  Array<{ id: number; label: string; isSelected: boolean }>
> => {
  const categories = await fetchCategories();

  return categories.map((category, index) => ({
    id: index + 1,
    label: category.name,
    isSelected: false,
  }));
};
