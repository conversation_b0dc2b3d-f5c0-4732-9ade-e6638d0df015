import AppInstance from '../config/axiosInstance';
import {IUser} from '../models';

//SIGNUP
async function SignUp(
  name: string,
  email: string,
  phoneNumber: string,
  password: string,
  gender: string,
  userType: string,
  fcmTokens: string[],
) {
  const response = await AppInstance({
    url: '/auth/signup',
    method: 'POST',
    data: {
      name,
      email,
      userType,
      phoneNumber,
      password,
      gender,
      fcmTokens,
    },
  });
  return response;
}

async function Login(email: string, password: string, fcmTokens: string[]) {
  const response = await AppInstance({
    url: '/auth/signin',
    method: 'POST',
    data: {
      email,
      password,
      fcmTokens,
    },
  });
  return response;
}

async function ForgotPassword(email: string) {
  const response = await AppInstance({
    url: '/auth/forgot-password',
    method: 'POST',
    data: {
      email,
    },
  });
  return response;
}

async function VerifyOtp(email: string, otp: string) {
  const response = await AppInstance({
    url: '/auth/verifyOtp',
    method: 'POST',
    data: {
      email,
      otp,
    },
  });
  return response;
}

async function ResetPassword(email: string, password: string) {
  const response = await AppInstance({
    url: '/auth/reset-password',
    method: 'POST',
    data: {
      email,
      password,
    },
  });
  return response;
}

async function UpdateUser(userId: string, {data}: {data: Partial<IUser>}) {
  const response = await AppInstance({
    url: `/users/${userId}`,
    method: 'PATCH',
    data,
  });
  return response;
}

async function UploadPicture(data: any) {
  const response = await AppInstance({
    url: `/upload-image`,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  });
  return response;
}

async function GetUserById(userId: string) {
  const response = await AppInstance({
    url: `/users/${userId}`,
    method: 'GET',
  });
  return response;
}

const AuthServices = {
  SignUp,
  Login,
  ForgotPassword,
  VerifyOtp,
  ResetPassword,
  UpdateUser,
  UploadPicture,
  GetUserById,
};

export default AuthServices;
