import AppInstance from "../config/axiosInstance";

async function GetEarningByDoctor(doctorId: string, params: any) {
  const response = await AppInstance({
    url: `/earnings/doctor/${doctorId}`,
    method: "GET",
    params,
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
  return response;
}

async function GetActiveBookingsForEarning(doctorId: string) {
  const response = await AppInstance({
    url: `/earnings/doctor/activeBookinngs/${doctorId}`,
    method: "GET",
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
  return response;
}

async function GetAccountBalance(doctorId: string) {
  const response = await AppInstance({
    url: `/payments/balance/${doctorId}`,
    method: "GET",
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
  return response;
}

async function TakeWidthdraw(data: any) {
  const response = await AppInstance({
    url: `/payments/payout`,
    method: "POST",
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
    data,
  });
  return response;
}

const EarningService = {
  GetEarningByDoctor,
  GetActiveBookingsForEarning,
  GetAccountBalance,
  TakeWidthdraw
};

export default EarningService;
