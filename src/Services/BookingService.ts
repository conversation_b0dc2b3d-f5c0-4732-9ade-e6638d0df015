import AppInstance from "../config/axiosInstance";

async function CreateBooking(payload: any) {
  const response = await AppInstance({
    url: "/bookings",
    method: "POST",
    data: payload,
  });
  return response;
}

async function GetBookings(params = {} as any) {
  const response = await AppInstance({
    url: "/bookings",
    method: "GET",
    params,
  });
  return response;
}

async function MarkBookingComplete(bookingId: string) {
  const response = await AppInstance({
    url: `/bookings/mark-as-completed/${bookingId}`,
    method: "PATCH",
  });
  return response;
}

async function UpdateBooking(bookingId: string, data = {} as any) {
  const response = await AppInstance({
    url: `/bookings/${bookingId}`,
    method: "PATCH",
    data,
  });
  return response;
}

async function GetBookingsByDateFilter(params = {} as any) {
  const response = await AppInstance({
    url: "/bookings",
    method: "GET",
    params,
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
  return response;
}

async function reschduleBookingApi(bookingId: string, payload: any) {
  const response = await AppInstance({
    url: `/bookings/reschedule/${bookingId}`,
    method: "PATCH",
    data: payload,
  });
  return response;
}

async function DeleteBooking(bookingId: string) {
  const response = await AppInstance({
    url: `/bookings/${bookingId}`,
    method: "DELETE",
  });
  return response;
}

const BookingService = {
  CreateBooking,
  GetBookings,
  UpdateBooking,
  MarkBookingComplete,
  GetBookingsByDateFilter,
  reschduleBookingApi,
  DeleteBooking,
};

export default BookingService;
