import AppInstance from "../config/axiosInstance";
import { ICreatePaymentIntentPayload } from "../models/IPayment";

async function GetPaymentMethods() {
  const response = await AppInstance({
    url: "/all-methods",
    method: "GET",
  });
  return response;
}

async function CreateConnectedAccount() {
  const response = await AppInstance({
    url: "/payments/create-connected-account",
    method: "POST",
  });
  return response;
}

async function CreateOnboardingLink(accountId: string) {
  const response = await AppInstance({
    url: `/payments/create-onboarding-link/${accountId}`,
    method: "POST",
  });
  return response;
}

async function CreatePaymentIntent(payload: ICreatePaymentIntentPayload) {
  const response = await AppInstance({
    url: `/payments/create-payment-intent`,
    method: "POST",
    data: payload,
  });
  return response;
}

const PaymentServices = {
  CreatePaymentIntent,
  GetPaymentMethods,
  CreateConnectedAccount,
  CreateOnboardingLink,
};

export default PaymentServices;
