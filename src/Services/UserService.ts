import AppInstance from "../config/axiosInstance";

async function GetUsers(params: any) {
  const response = await AppInstance({
    method: "GET",
    url: "/users",
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
    params,
  });
  return response;
}

async function GetUserById(userId: string) {
  const response = await AppInstance({
    method: "GET",
    url: `/users/${userId}`,
  });
  return response;
}

async function AvailableSlots(userId: string, availableDate: string, sessionTime: number, timeOfDay?: string | "") {
  const response = await AppInstance({
    method: "GET",
    url: `users/availableSlots/${userId}/${availableDate}`,
    params: {
      timeOfDay: timeOfDay,
      interval: sessionTime,
    },
  });
  return response;
}

async function ContactSupportAPI(name: string, email: string, message: string) {
  const payload = {
    name: name,
    email: email,
    message: message,
  };

  const response = await AppInstance({
    method: "POST",
    url: `/support`,
    data: payload,
  });
  return response;
}
async function DeleteUserAccount(userId: string) {
  const response = await AppInstance({
    method: "DELETE",
    url: `/users/${userId}`,
  });
  return response;
}

async function GetTodayBooking(doctorId: string) {
  return await AppInstance({
    url: `/users/todayAppointments/${doctorId}`,
    method: "GET",
    headers: {
      "Cache-Control": "no-cache, no-store, must-revalidate",
    },
  });
}

const UserService = {
  GetUsers,
  GetUserById,
  AvailableSlots,
  ContactSupportAPI,
  DeleteUserAccount,
  GetTodayBooking,
};

export default UserService;
