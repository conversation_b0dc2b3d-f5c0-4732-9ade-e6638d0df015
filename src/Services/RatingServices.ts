import AppInstance from '../config/axiosInstance';

interface AddRatingPayload {
  rating: number;
  review: string;
  reviewBy: string;
}
async function AddRating(doctorId: string, payload: AddRatingPayload) {
  const response = await AppInstance({
    url: `/reviews/${doctorId}`,
    method: 'POST',
    data: payload,
  });
  return response;
}
async function getDoctorReviewbyId(doctorId: string) {
  const response = await AppInstance({
    url: `/reviews/${doctorId}`,
    method: 'GET',
  });
  return response;
}

const RatingServices = {
  AddRating,
  getDoctorReviewbyId,
};

export default RatingServices;
