import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";

const AppInstance = axios.create({
  baseURL: "https://ow-server-two.vercel.app/api",
  // baseURL: "https://ow-server-bice.vercel.app/api",
  // baseURL: "https://ow-server-lake.vercel.app/api",
});

AppInstance.interceptors.request.use(async (request) => {
  const access_token = await AsyncStorage.getItem("accessToken");
  if (access_token) {
    request.headers.Authorization = `Bearer ${access_token}`;
  }

  return request;
});

AppInstance.interceptors.response.use((response) => {
  return response;
});

export default AppInstance;
