import { Platform, StatusBar, StyleSheet } from "react-native";

export const COLORS = {
  white: "#FFFFFF",
  black: "#000000",
  primary: "#EB4E1F",
  borderColor: "#EB4E1F60",
  red: "#FF3D00",
};
export const FONTS = {
  Nunito_Black: "Nunito-Black",
  Nunito_Bold: "Nunito-Bold",
  Nunito_ExtraBold: "Nunito-ExtraBold",
  Nunito_ExtraLight: "Nunito-ExtraLight",
  Nunito_Light: "Nunito-Light",
  Nunito_Medium: "Nunito-Medium",
  Nunito_Regular: "Nunito-Regular",
  Nunito_SemiBold: "Nunito-SemiBold",
};

export const sizes = {
  paddingTop: 0,
  // paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
};

export const appStyles = StyleSheet.create({
  errorText: {
    fontSize: 12,
    color: COLORS.red,
    paddingTop: 5,
    paddingLeft: 5,
  },
  loadingContainer: {
    paddingTop: 80,
    flexDirection: "row",
    alignSelf: "center",
    gap: 10,
  },
  loadingParagraph: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Medium,
  },
  line: {
    width: 70,
    height: 6,
    backgroundColor: "#00000010",
    alignSelf: "center",
    borderRadius: 12 / 2,
  },
});
