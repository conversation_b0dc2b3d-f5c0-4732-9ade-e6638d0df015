import {StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import {Platform} from 'react-native';
import {COLORS, FONTS} from '../../../themes';
import RNPickerSelect from 'react-native-picker-select';
import {Card, DropDown} from '../../../assets/svgicons';
import FormInput from '../../../components/FormInput';
import {TextButton} from '../../../components';
import Modal from 'react-native-modal';
import {useUser} from '../../../Hooks/UseContext';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';

import {useFormik} from 'formik';
import * as Yup from 'yup';
type Props = NativeStackScreenProps<AuthStackParamList, 'CheckOut'>;

const CheckOut: React.FC<Props> = ({route, navigation}) => {
  const {setUserId} = useUser();
  const [isModalVisible, setModalVisible] = React.useState(false);
  const validationSchema = Yup.object().shape({
    country: Yup.string().required('Country is required'),
    cardNumber: Yup.string()
      .required('Card number is required')
      .matches(/^\d{16}$/, 'Card number is invalid'),
    cardName: Yup.string().required('Cardholder name is required'),
    cvv: Yup.string()
      .required('CVC is required')
      .matches(/^\d{3,4}$/, 'Invalid CVC'),
    expiryDate: Yup.string()
      .required('Expiry date is required')
      .matches(/^\d{2}\/\d{2}$/, 'Invalid date format (MM/YY)'),
  });

  const {handleChange, handleBlur, handleSubmit, values, errors, touched} =
    useFormik({
      initialValues: {
        country: '',
        cardNumber: '',
        cardName: '',
        cvv: '',
        expiryDate: '',
      },
      validationSchema,
      onSubmit: () => {
        setUserId('uid');
        toggleModal();
      },
    });

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const rederViewCalendarModal = () => {
    return (
      <Modal isVisible={isModalVisible} style={{margin: 20}}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>
            Great!! Your appointment has been made
          </Text>
          <TextButton
            label={'View My Calendar'}
            containerStyle={
              [styles.getStartedButton, {marginTop: 18}] as ViewStyle
            }
            onPress={() => {
              toggleModal();
              setTimeout(() => {
                navigation.navigate('Agenda');
              }, 500);
            }}
            labelStyle={{fontFamily: FONTS.Nunito_Regular}}
          />
        </View>
      </Modal>
    );
  };
  return (
    <View style={styles.container}>
      <View style={{marginTop: 30, marginHorizontal: 20, flex: 1}}>
        {/* TITLE */}
        <Text style={styles.title}>Stripe</Text>
        {/* PICKER */}
        <View style={styles.pickerContainer}>
          <RNPickerSelect
            placeholder={{}}
            onValueChange={handleChange('country')}
            items={[
              {label: 'United State', value: 'United State'},
              {label: 'England', value: 'England'},
            ]}
            useNativeAndroidPickerStyle={false}
            Icon={() => <DropDown />} // Change this line
            style={{
              iconContainer: {
                top: Platform.OS === 'ios' ? 5 : 22,
                right: 0,
              },
              inputAndroid: {
                fontFamily: FONTS.Nunito_Regular,
              },
              inputIOS: {
                fontFamily: FONTS.Nunito_Regular,
              },
            }}
          />
          {touched.country && errors.country && (
            <Text style={styles.errorText}>{errors.country}</Text>
          )}
        </View>
        {/* CARD_NUMBER_INPUT_FEILDS */}
        <FormInput
          placeholder={'Card number'}
          placeholderTextColor={'#00000070'}
          leftIcon={<Card />}
          leftIconStyle={{marginRight: 10}}
          containerStyle={{marginTop: 20, borderColor: '#E8E8E8'}}
          onChangeText={handleChange('cardNumber')}
          onBlur={handleBlur('cardNumber')}
          value={values.cardNumber}
          errorMessage={
            touched.cardNumber && errors.cardNumber ? errors.cardNumber : ''
          }
        />
        {/* CARD_NAME_INPUT_FIELDS */}
        <FormInput
          placeholder={'Card holder name'}
          containerStyle={styles.nameInputfieldContainer}
          placeholderTextColor={'#00000070'}
          onChangeText={handleChange('cardName')}
          onBlur={handleBlur('cardName')}
          value={values.cardName}
          errorMessage={
            touched.cardName && errors.cardName ? errors.cardName : ''
          }
        />
        <View
          style={{
            flexDirection: 'row',
            gap: 16,
            marginTop: 20,
          }}>
          {/* CVC_INPUT_FEILDS */}
          <View style={{width: '48%'}}>
            <FormInput
              placeholder={'CVC/CVV'}
              containerStyle={styles.cvcInputfieldContainer}
              placeholderTextColor={'#00000070'}
              style={{width: '100%', height: '100%'}}
              onChangeText={handleChange('cvv')}
              onBlur={handleBlur('cvv')}
              value={values.cvv}
              errorMessage={touched.cvv && errors.cvv ? errors.cvv : ''}
            />
          </View>
          {/*DATE_INPUT_FEILDS */}
          <View style={{width: '48%'}}>
            <FormInput
              placeholder={'MM/YY'}
              containerStyle={styles.date}
              placeholderTextColor={'#00000070'}
              style={{flex: 1, height: '100%'}}
              onChangeText={handleChange('expiryDate')}
              onBlur={handleBlur('expiryDate')}
              value={values.expiryDate}
              errorMessage={
                touched.expiryDate && errors.expiryDate ? errors.expiryDate : ''
              }
            />
          </View>
        </View>
      </View>
      <View style={styles.footer}>
        <TextButton
          label={'Lets Get Started'}
          containerStyle={styles.getStartedButton}
          onPress={handleSubmit}
        />
        {/* INFO */}
        <Text style={styles.text}>
          Lorem ipsum dolor sit amet consectetur. Eget varius est posuere augue
          cursus suspendisse.
        </Text>
      </View>
      {rederViewCalendarModal()}
    </View>
  );
};

export default CheckOut;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  pickerContainer: {
    backgroundColor: '#F9F9F9',
    height: 55,
    borderRadius: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E8E8E8',
    marginTop: 20,
  },
  title: {
    fontSize: 18,
    color: COLORS.black,
  },
  nameInputfieldContainer: {
    paddingLeft: 20,
    marginTop: 20,
    borderColor: '#E8E8E8',
  },
  cvcInputfieldContainer: {
    borderColor: '#E8E8E8',
  },
  date: {
    borderColor: '#E8E8E8',
    // flex: 1,
  },
  footer: {
    flex: 1,
    paddingTop: 73,
    marginHorizontal: 20,
  },
  text: {
    fontSize: 12,
    color: '#AAAAAA',
    textAlign: 'center',
    paddingTop: 30,
    fontFamily: FONTS.Nunito_Regular,
    marginHorizontal: 20,
  },
  getStartedButton: {
    height: 50,
    backgroundColor: COLORS.red,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 14,
    elevation: 17,
    width: '100%',
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    alignItems: 'center',
    padding: 20,
    borderRadius: 10,
  },
  modalTitle: {
    fontSize: 16,
    fontFamily: FONTS.Nunito_Bold,
    color: COLORS.black,
  },
});
