import { SafeAreaView, Text, View, ScrollView, PushNotificationIOS, Platform } from "react-native";
import React, { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { appStyles } from "../../../themes";
import RNPickerSelect from "react-native-picker-select";
import { Call, DropDown, Email, Gender, Lock, User, OW_LOGO } from "../../../assets/svgicons";
import { AppButton, Forminput, TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { setHasSelectedRole, setUserAction, setUserId } from "../../../redux/slices/AuthSlice";
import { styles } from "./styles";
import AuthServices from "../../../Services/AuthServices";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { firebase } from "@react-native-firebase/firestore";
import { IUser } from "../../../models";
import useApiHandler from "../../../Hooks/useApiHandler";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

type Props = NativeStackScreenProps<AuthStackParamList, "SignUp">;

// Define Yup validation schema
const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string().email("Invalid email").required("Email is required"),
  phoneNumber: Yup.string().min(7, "Phone number is too short").required("Phone number is required"),
  password: Yup.string().required("Password is required").min(8, "Password must be at least 8 characters long"),
  confirmPassword: Yup.string()
    .required("Confirm Password is required")
    .oneOf([Yup.ref("password")], "Passwords must match")
    .min(8, "Password must be at least 8 characters long"),
  gender: Yup.string().nullable().required("Please select a gender"),
});

interface SignUpForm {
  name: string;
  email: string;
  phoneNumber: string;
  confirmPassword: string;
  password: string;
  gender: string | null;
  role: string;
}

const SignUp: React.FC<Props> = ({ navigation }) => {
  const [passwordVisible, setPassworldVisble] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const { handleApiErrors } = useApiHandler();
  const dispatch = useAppDispatch();
  const selectedRole = useAppSelector((state) => state.auth.chooseRole.selectedRole);

  const fcmTokens = useAppSelector((state) => state.fcmTokenSlice.fcmToken);

  const addUserToFirestore = async (user: IUser) => {
    firebase
      .firestore()
      .collection("Users")
      .doc(user._id)
      .set({
        ...user,
        receiveNotifications: false,
      });
  };

  // Formik setup
  const formik = useFormik<SignUpForm>({
    initialValues: {
      name: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      gender: null,
      role: selectedRole,
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: async (values) => {
      setIsLoading(true);
      try {
        const resp = await AuthServices.SignUp(
          values.name,
          values.email,
          values.phoneNumber,
          values.password,
          values.gender,
          selectedRole,
          [fcmTokens],
        );
        if (resp.status == 201) {
          await AsyncStorage.setItem("accessToken", resp.data.accessToken);
          const { accessToken, ...user } = resp.data;

          await addUserToFirestore(user);
          if (selectedRole === "doctor") {
            navigation.navigate("Welcome", { userId: user._id });
            dispatch(setUserAction({ ...user, receiveNotifications: false }));
            return;
          } else {
            dispatch(setUserAction({ ...user, receiveNotifications: false }));
            // navigation.navigate('Subscribe');
            dispatch(setUserId(user._id));
            return;
          }
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView
        enableOnAndroid={true}
        extraScrollHeight={Platform.OS === "android" ? 0 : 20}
        showsVerticalScrollIndicator={false}
      >
        <OW_LOGO style={styles.logo} />
        <Text style={styles.title}>Sign Up</Text>
        <Text style={styles.paragraph}>Please enter your details.</Text>
        <View style={{ marginHorizontal: 20, marginTop: 24 }}>
          <Forminput
            placeholder={"Name"}
            placeholderTextColor="#292D3260"
            leftIcon={<User />}
            containerStyle={{ marginTop: 12 }}
            onChangeText={formik.handleChange("name")}
            onBlur={formik.handleBlur("name")}
            value={formik.values.name}
            errorMessage={formik.touched.name && formik.errors.name}
          />
          <Forminput
            placeholder={"Email"}
            placeholderTextColor="#292D3260"
            leftIcon={<Email />}
            containerStyle={{ marginTop: 12 }}
            onChangeText={formik.handleChange("email")}
            onBlur={formik.handleBlur("email")}
            value={formik.values.email}
            keyboardType="email-address"
            autoCapitalize="none"
            errorMessage={formik.touched.email && formik.errors.email}
          />
          {/* PHONE_NUMBER_INPUT_FIELDS */}
          <Forminput
            placeholder={"Phone Number"}
            placeholderTextColor="#292D3260"
            leftIcon={<Call />}
            containerStyle={{ marginTop: 12 }}
            onChangeText={formik.handleChange("phoneNumber")}
            onBlur={formik.handleBlur("phoneNumber")}
            value={formik.values.phoneNumber}
            keyboardType="phone-pad"
            errorMessage={formik.touched.phoneNumber && formik.errors.phoneNumber}
          />
          {/* GENDER PICKER */}
          <View style={styles.pickerContainer}>
            <View style={{ position: "absolute", left: 10 }}>
              <Gender />
            </View>
            <RNPickerSelect
              placeholder={{
                label: "Gender",
                value: null,
                color: "#898B8E",
              }}
              onValueChange={(value) => {
                formik.setFieldValue("gender", value);
              }}
              onClose={() => formik.handleBlur("gender")}
              items={[
                { label: "Male", value: "male" },
                { label: "Female", value: "female" },
              ]}
              value={formik.values.gender}
              useNativeAndroidPickerStyle={false}
              Icon={() => <DropDown />}
              style={{
                iconContainer: styles.iconContainer,
                inputAndroid: styles.inputAndroid,
                inputIOS: styles.inputIOS,
              }}
            />
          </View>

          {formik.touched.gender && formik.errors.gender && (
            <Text style={appStyles.errorText}>{formik.errors.gender}</Text>
          )}
          {/* ROLE PICKER */}
          <View style={styles.pickerContainer}>
            <View style={{ position: "absolute", left: 10 }}>
              <User />
            </View>
            <RNPickerSelect
              placeholder={{
                label: "Select Role",
                value: null,
                color: "#898B8E",
              }}
              onValueChange={(value) => {
                dispatch(
                  setHasSelectedRole({
                    selectedRole: value,
                    hasSelectedRole: true,
                  }),
                );
              }}
              items={[
                { label: "Doctor", value: "doctor" },
                { label: "Patient", value: "patient" },
              ]}
              value={selectedRole}
              useNativeAndroidPickerStyle={false}
              Icon={() => <DropDown />}
              style={{
                iconContainer: styles.iconContainer,
                inputAndroid: styles.inputAndroid,
                inputIOS: styles.inputIOS,
              }}
            />
            {!selectedRole && <Text style={appStyles.errorText}>Please select a role</Text>}
          </View>
          <Forminput
            placeholder={"Password"}
            placeholderTextColor="#292D3260"
            leftIcon={<Lock />}
            containerStyle={{ marginTop: 12 }}
            onChangeText={formik.handleChange("password")}
            onBlur={formik.handleBlur("password")}
            value={formik.values.password}
            errorMessage={formik.touched.password && formik.errors.password}
            isPassword
            secureTextEntry={passwordVisible}
            onPressRightIcon={() => setPassworldVisble(!passwordVisible)}
          />

          <Forminput
            placeholder={"Confirm Password"}
            placeholderTextColor="#292D3260"
            leftIcon={<Lock />}
            containerStyle={{ marginTop: 12 }}
            onChangeText={formik.handleChange("confirmPassword")}
            onBlur={formik.handleBlur("confirmPassword")}
            value={formik.values.confirmPassword}
            errorMessage={formik.touched.confirmPassword && formik.errors.confirmPassword}
            isPassword
            secureTextEntry={passwordVisible}
            onPressRightIcon={() => setPassworldVisble(!passwordVisible)}
          />
        </View>

        <AppButton
          title="Sign Up"
          onPress={formik.handleSubmit}
          disabled={!formik.isValid || isLoading}
          isLoading={isLoading}
        />

        {/* SOCIAL SIGN UP OPTIONS */}
        {/* {route.params.key === 'doctor' ? (
          <View style={styles.socialMediaButtonContainer}>
            <ButtonwithIcon
              icon={<GoogleLogo />}
              containerStyle={styles.button}
            />
            <ButtonwithIcon icon={<FB />} containerStyle={styles.button} />
            <ButtonwithIcon icon={<Apple />} containerStyle={styles.button} />
          </View>
        ) : (
          <View>
            <TextButtonwithIcon
              label={'Continue with Google'}
              leftIcon={<GoogleLogo />}
              labelStyle={styles.googleButtonLabel}
              containerStyle={styles.googleButton}
            />
          </View>
        )} */}

        {/* FOOTER */}
        <View style={styles.footer}>
          <View style={{ flexDirection: "row" }}>
            <Text style={styles.accountText}>Already have an account? </Text>
            <TextButton
              label={"Log In here"}
              labelStyle={styles.signUpButtonLabel}
              onPress={() => navigation.navigate("Login")}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default SignUp;
