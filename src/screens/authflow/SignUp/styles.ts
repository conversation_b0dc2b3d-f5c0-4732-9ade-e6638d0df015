import { Platform, StyleSheet } from "react-native";
import { COLORS, FONTS, sizes } from "../../../themes";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  logo: {
    width: 167,
    height: 47,
    alignSelf: "center",
    marginTop: 25,
  },
  title: {
    marginTop: 35,
    fontSize: 24,
    color: "#030F1C",
    marginHorizontal: 20,
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    marginTop: 7,
    fontSize: 14,
    color: "#8D8D8D",
    marginHorizontal: 20,
    fontFamily: FONTS.Nunito_Regular,
  },
  forgotPasswordButton: {
    alignSelf: "flex-end",
    marginTop: 5,
    fontSize: 14,
    marginRight: 20,
  },
  pickerContainer: {
    backgroundColor: "#F9F9F9",
    height: 55,
    borderRadius: 10,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: COLORS.borderColor,
    marginTop: 12,
    paddingLeft: 50,
    paddingRight: 20,
  },
  dividerContainer: {
    flexDirection: "row",
    marginTop: 50,
    marginHorizontal: 59,
    alignItems: "center",
  },
  lineStyles: {
    height: 1,
    backgroundColor: "#585C60",
    flex: 1,
  },
  text: {
    marginHorizontal: 3,
    fontSize: 14,
    color: "#585C60",
    fontFamily: FONTS.Nunito_Regular,
  },
  googleButton: {
    backgroundColor: "#F2F2F2",
    marginHorizontal: 30,
    paddingLeft: 16,
    paddingVertical: 12,
    marginTop: 40,
    borderRadius: 8,
  },
  footer: {
    marginVertical: 30,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  socialMediaButtonContainer: {
    marginTop: 40,
    alignSelf: "center",
    flexDirection: "row",
    gap: 10,
  },
  button: {
    width: 52,
    height: 52,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: 8,
  },
  googleButtonLabel: {
    color: "#353535",
    marginLeft: 15,
    fontFamily: FONTS.Nunito_Light,
  },
  accountText: {
    color: "#8D8D8D",
    fontSize: 15,
    fontFamily: FONTS.Nunito_Light,
  },
  signUpButtonLabel: {
    color: COLORS.primary,
    marginLeft: 7,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  inputAndroid: {
    color: "#000",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  inputIOS: {
    color: "#898B8E",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  iconContainer: {
    top: Platform.OS === "ios" ? 5 : 22,
    right: 0,
  },
});
