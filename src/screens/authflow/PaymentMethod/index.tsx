import {FlatList, StyleSheet, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {
  Circle,
  CircleFilled,
  MasterCard,
  NotoCard,
  Paypal,
} from '../../../assets/svgicons';
import {PaymentCard, TextButton} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {AxiosError} from 'axios';
import PaymentServices from '../../../Services/PaymentServices';
import {IPaymentMethod} from '../../../models/IPayment';
type Props = NativeStackScreenProps<AuthStackParamList, 'PaymentMethod'>;

const PaymentMethod: React.FC<Props> = ({navigation}) => {
  const [data, setData] = useState<IPaymentMethod[]>([]);
  const [selectedCard, setSelectedCard] = useState('');

  const getPaymentMethods = async () => {
    try {
      const resp = await PaymentServices.GetPaymentMethods();
      if (resp.status == 200) {
        setData(resp.data);
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error fetching payment methods', err);
    }
  };
  useEffect(() => {
    getPaymentMethods();
  }, []);

  const renderPaymentOption = ({item}: {item: IPaymentMethod}) => (
    <PaymentCard
      label={item.name}
      logo={
        item.name === 'Stripe' ? (
          <MasterCard />
        ) : item.name === 'CREDIT CARD' ? (
          <NotoCard />
        ) : (
          <Paypal />
        )
      }
      selectedCard={selectedCard === item.name ? <CircleFilled /> : <Circle />}
      onPress={() => setSelectedCard(item.name)}
      containerStyle={{marginTop: 14}}
    />
  );
  return (
    <View style={styles.container}>
      {/* PAYMENT_OPTIONS */}
      <FlatList
        data={data}
        keyExtractor={item => item.id.toString()}
        renderItem={renderPaymentOption}
        // contentContainerStyle={{paddingTop: 30}}
        showsVerticalScrollIndicator={false}
      />
      {/* FOOTER */}
      <View style={styles.footer}>
        <TextButton
          label={'Select'}
          containerStyle={{
            ...styles.selectButton,
            backgroundColor: selectedCard ? '#EB4E1F' : '#EB4E1F50',
          }}
          disabled={!selectedCard}
          onPress={() => navigation.navigate('CheckOut')}
        />
      </View>
    </View>
  );
};

export default PaymentMethod;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 30,
  },
  selectButton: {
    height: 50,
    borderRadius: 8,
    backgroundColor: '#EB4E1F40',
  },
  footer: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 60,
    marginHorizontal: 20,
  },
});
