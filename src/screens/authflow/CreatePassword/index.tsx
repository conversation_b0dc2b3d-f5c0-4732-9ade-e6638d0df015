import {Image, SafeAreaView, ScrollView, Text, View} from 'react-native';
import React, {useState} from 'react';
import {CreatPassword} from '../../../assets/images';
import {Forminput, TextButton} from '../../../components';
import {Eye, Lock} from '../../../assets/svgicons';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import AuthServices from '../../../Services/AuthServices';
import Toast from 'react-native-toast-message';
import {styles} from './styles';
import useApiHandler from '../../../Hooks/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
type Props = NativeStackScreenProps<AuthStackParamList, 'CreatePassword'>;

const CreatePassword: React.FC<Props> = ({navigation, route}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [passwordVisible, setPassworldVisble] = useState(true);
  const {handleApiErrors} = useApiHandler();

  const {email} = route.params;

  const formik = useFormik({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validationSchema: Yup.object({
      password: Yup.string()
        .min(8, 'Password must be at least 8 characters')
        .required('Password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords must match')
        .required('Confirm password is required'),
    }),
    onSubmit: async values => {
      try {
        setIsLoading(true);
        const resp = await AuthServices.ResetPassword(email, values.password);
        if (resp.status == 201) {
          navigation.popToTop();
          Toast.show({
            type: 'success',
            text1: 'Password reset',
            text2: 'Password reset successfully',
          });
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });
  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <Image source={CreatPassword} style={styles.image} />
        {/* TITLE */}
        <Text style={styles.title}>Reset Password</Text>
        {/* PARAGRAPH */}
        <Text style={styles.paragraph}>
          Enter your new password try not to forget again
        </Text>
        {/* FOOTER */}
        <View style={styles.footer}>
          {/* PASSWORD_INPUT_FEILDS */}
          <Forminput
            placeholder={'Password'}
            placeholderTextColor="#292D3260"
            leftIcon={<Lock />}
            isPassword
            containerStyle={{marginTop: 12}}
            secureTextEntry={passwordVisible}
            onChangeText={formik.handleChange('password')}
            onBlur={formik.handleBlur('password')}
            value={formik.values.password}
            errorMessage={formik.touched.password && formik.errors.password}
            onPressRightIcon={() => setPassworldVisble(!passwordVisible)}
          />
          {/* PASSWORD_INPUT_FEILDS */}
          <Forminput
            placeholder={'Confirm Password'}
            placeholderTextColor="#292D3260"
            leftIcon={<Lock />}
            isPassword
            secureTextEntry={passwordVisible}
            containerStyle={{marginTop: 14}}
            value={formik.values.confirmPassword}
            onChangeText={formik.handleChange('confirmPassword')}
            onBlur={formik.handleBlur('confirmPassword')}
            onPressRightIcon={() => setPassworldVisble(!passwordVisible)}
            errorMessage={
              formik.errors.confirmPassword && formik.touched.confirmPassword
                ? formik.errors.confirmPassword
                : ''
            }
          />
          <TextButton
            label={'Reset Password'}
            labelStyle={styles.resetLabel}
            containerStyle={styles.button}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={isLoading || !formik.isValid}
          />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default CreatePassword;
