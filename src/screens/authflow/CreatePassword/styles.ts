import {StyleSheet} from 'react-native';
import {COLORS, FONTS} from '../../../themes';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  image: {
    width: 300,
    height: 332,
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  title: {
    fontSize: 24,
    color: COLORS.black,
    textAlign: 'center',
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.black,
    marginHorizontal: 50,
    textAlign: 'center',
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    flex: 1,
    paddingVertical: 41,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  button: {
    height: 52,
    backgroundColor: COLORS.primary,
    marginHorizontal: 30,
    borderRadius: 10,
    marginTop: 20,
    width: '100%',
  },
  resetLabel: {color: COLORS.white, fontSize: 16},
});
