import { SafeAreaView, Text, Image, View, StatusBar } from "react-native";
import React, { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Email, Eye, Lock, OW_LOGO } from "../../../assets/svgicons";
import { AppButton, Forminput, TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { setUserAction, setUserId } from "../../../redux/slices/AuthSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AuthServices from "../../../Services/AuthServices";
import { styles } from "./styles";
import { IUser } from "../../../models";
import { firebase } from "@react-native-firebase/firestore";
import useApiHand<PERSON> from "../../../Hooks/useApiHandler";

type Props = NativeStackScreenProps<AuthStackParamList, "Login">;

const validationSchema = Yup.object().shape({
  email: Yup.string().email("Invalid email").required("Email is required"),
  password: Yup.string().required("Password is required").min(6, "Password must be at least 6 characters long"),
});

interface SignInForm {
  email: string;
  password: string;
}

const Login: React.FC<Props> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [passwordVisible, setPassworldVisble] = useState(true);
  const { handleApiErrors } = useApiHandler();
  const fcmTokens = useAppSelector((state) => state.fcmTokenSlice.fcmToken);

  const dispatch = useAppDispatch();

  const formik = useFormik<SignInForm>({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: async (values) => {
      try {
        setIsLoading(true);
        const resp = await AuthServices.Login(values.email, values.password, [fcmTokens]);
        if (resp.status == 200) {
          const { accessToken, ...user } = resp.data;
          await AsyncStorage.setItem("accessToken", accessToken);
          // const userDoc = await firebase
          //   .firestore()
          //   .collection('Users')
          //   .doc(user._id)
          //   .get();
          // const userData = userDoc.data();

          checkOnboardStep({
            ...user,
          });
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  // * CHECK USER DATA & NAVIGATE RESPECTIVE STEP
  function checkOnboardStep(user: IUser) {
    if (user.userType === "doctor") {
      if (!user?.photo) {
        navigation.navigate("Welcome", { userId: user._id });
      } else if (!user.introduction) {
        navigation.navigate("ProfileVerificationStep2", { userId: user._id });
      } else if (!user?.workExperience?.years) {
        navigation.navigate("ProfileVerificationStep3", { userId: user._id });
      } else if (!user?.schedule?.length) {
        navigation.navigate("ProfileVerificationStep4", { userId: user._id });
      }
      // else if (!user?.services?.length) {
      //   navigation.navigate('ProfileVerificationStep5', {userId: user._id});
      // } else if (!user?.eligibilityConfirmation?.degree) {
      //   navigation.navigate('ProfileVerificationStep7', {userId: user._id});
      // }
      else {
        dispatch(setUserId(user._id));
        dispatch(setUserAction(user));
      }
    } else {
      dispatch(setUserId(user._id));
      dispatch(setUserAction(user));
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <OW_LOGO style={styles.logo} />
      <Text style={styles.title}>Welcome Back</Text>
      <Text style={styles.paragraph}>Welcome back! Please enter your details.</Text>

      <View style={{ marginHorizontal: 20, marginTop: 24 }}>
        <Forminput
          placeholder={"Email"}
          placeholderTextColor="#292D3260"
          leftIcon={<Email />}
          onChangeText={formik.handleChange("email")}
          onBlur={formik.handleBlur("email")}
          value={formik.values.email}
          keyboardType="email-address"
          autoCapitalize="none"
          errorMessage={formik.touched.email && formik.errors.email}
        />
        <Forminput
          placeholder={"Password"}
          placeholderTextColor="#292D3260"
          leftIcon={<Lock />}
          isPassword
          containerStyle={{ marginTop: 12 }}
          secureTextEntry={passwordVisible}
          onChangeText={formik.handleChange("password")}
          onBlur={formik.handleBlur("password")}
          value={formik.values.password}
          errorMessage={formik.touched.password && formik.errors.password}
          onPressRightIcon={() => setPassworldVisble(!passwordVisible)}
        />
      </View>

      <TextButton
        label={"Forgot Password?"}
        labelStyle={styles.forgotPasswordLabel}
        containerStyle={styles.forgotPasswordButton}
        onPress={() => navigation.navigate("ForgotPassword")}
      />

      <AppButton
        title="Log In"
        onPress={formik.handleSubmit}
        disabled={!formik.isValid || isLoading}
        isLoading={isLoading}
      />

      <View style={styles.footer}>
        <View style={{ flexDirection: "row" }}>
          <Text style={styles.accountText}>Don’t have an account?</Text>
          <TextButton
            label={"Sign up here"}
            labelStyle={styles.signInButtonLabel}
            onPress={() => navigation.navigate("SignUp")}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Login;
