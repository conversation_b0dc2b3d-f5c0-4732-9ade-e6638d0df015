import { StyleSheet } from "react-native";
import { COLORS, FONTS, sizes } from "../../../themes";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  logo: {
    width: 167,
    height: 47,
    alignSelf: "center",
    marginTop: 25,
  },
  title: {
    marginTop: 35,
    fontSize: 24,
    color: "#030F1C",
    textAlign: "center",
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    marginTop: 7,
    fontSize: 14,
    color: "#8D8D8D",
    textAlign: "center",
    fontFamily: FONTS.Nunito_Regular,
  },
  forgotPasswordButton: {
    alignSelf: "flex-end",
    marginTop: 5,
    fontSize: 14,
    paddingRight: 20,
  },
  forgotPasswordLabel: {
    color: "#828282",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  loginButton: {
    height: 52,
    backgroundColor: COLORS.primary,
    marginHorizontal: 30,
    borderRadius: 10,
    marginTop: 38,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 14,
    elevation: 17,
  },
  footer: {
    flex: 1,
    marginBottom: 30,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  accountText: {
    color: "#8D8D8D",
    fontSize: 15,
    fontFamily: FONTS.Nunito_Light,
  },
  signInButtonLabel: {
    color: COLORS.primary,
    marginLeft: 7,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  loginLabel: { color: COLORS.white },
});
