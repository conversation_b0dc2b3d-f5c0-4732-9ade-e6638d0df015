import { StyleSheet, Text, View, SafeAreaView, Image, ScrollView, ImageSourcePropType } from "react-native";
import React, { useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { ButtonwithIcon, TextButton } from "../../../components";
import { Camera } from "../../../assets/svgicons";
import { useUser } from "../../../Hooks/UseContext";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useAppDispatch } from "../../../redux/config/Store";
import { setUserAction, setUserId } from "../../../redux/slices/AuthSlice";
import { useFormik } from "formik";
import * as Yup from "yup";
import ImageCropPicker, { ImageOrVideo } from "react-native-image-crop-picker";
import AuthServices from "../../../Services/AuthServices";
import { UploadedImageResponse } from "../../../models/User";
import { AxiosError } from "axios";
import useApiHandler from "../../../Hooks/useApiHandler";
type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep7">;

const validationSchema = Yup.object({
  degree: Yup.object().required("Degree is required"),
  license: Yup.object().required("License is required"),
});

const ProfileVerificationStep7: React.FC<Props> = ({ navigation, route }) => {
  const { userId } = route.params;

  const [degreeImage, setDegreeImage] = useState<ImageOrVideo>();
  const [licenseImage, setLicenseImage] = useState<ImageOrVideo>();
  const [isLoading, setLoading] = useState(false);

  const { setIsProfessional } = useUser();
  const dispatch = useAppDispatch();
  const { handleApiErrors } = useApiHandler();
  const hardCodedImage =
    "https://hips.hearstapps.com/hmg-prod/images/portrait-of-a-happy-young-doctor-in-his-clinic-royalty-free-image-**********.jpg?crop=0.66698xw:1xh;center,top&resize=1200:*";
  const formik = useFormik({
    initialValues: {
      degree: "",
      license: "",
    },
    // validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        const degree_formdata = new FormData();
        degree_formdata.append("file", {
          // uri: degreeImage?.path,
          uri: hardCodedImage,
          // type: degreeImage?.mime,
          type: "image/jpeg", // Assuming the image is in JPEG format
          name: userId + "_degree",
        });

        // const degree_upload: UploadedImageResponse = (await AuthServices.UploadPicture(degree_formdata)).data;

        const license_formdata = new FormData();
        // license_formdata.append("file", {
        //   uri: licenseImage?.path,
        //   type: licenseImage?.mime,
        //   name: userId + "_license",
        // });
        license_formdata.append("file", {
          uri: hardCodedImage,
          type: "image/jpeg", // Assuming the image is in JPEG format
          name: userId + "_license",
        });

        // const license_upload: UploadedImageResponse = (await AuthServices.UploadPicture(license_formdata)).data;

        const resp = await AuthServices.UpdateUser(userId, {
          data: {
            eligibilityConfirmation: {
              degree:
                "https://hips.hearstapps.com/hmg-prod/images/portrait-of-a-happy-young-doctor-in-his-clinic-royalty-free-image-**********.jpg?crop=0.66698xw:1xh;center,top&resize=1200:*",
              license:
                "https://hips.hearstapps.com/hmg-prod/images/portrait-of-a-happy-young-doctor-in-his-clinic-royalty-free-image-**********.jpg?crop=0.66698xw:1xh;center,top&resize=1200:*",
            },
          },
        });

        dispatch(setUserId(userId));
        dispatch(setUserAction(resp.data));
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setLoading(false);
      }
    },
  });

  const handleImagePicker = async () => {
    try {
      const image = await ImageCropPicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: "photo",
      });
      return image;
    } catch (error) {
      console.log("Error selecting image", error);
    }
  };

  async function handleSelectDegree() {
    const degree = await handleImagePicker();
    setDegreeImage(degree);
    formik.setFieldValue("degree", degree);
  }

  async function handleSelectLicense() {
    const license = await handleImagePicker();
    setLicenseImage(license);
    formik.setFieldValue("license", license);
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.innerContainer}>
          <View style={styles.header}>
            {/* HEADING */}
            <Text style={styles.heading}>Complete My Profile</Text>
            {/* SKIP */}
            <TextButton
              label={"SKIP"}
              labelStyle={{ color: COLORS.black }}
              containerStyle={styles.skip}
              onPress={() => dispatch(setUserId(userId))}
            />
          </View>
          {/* COUNTER */}
          <Text style={[styles.heading, { marginTop: 3 }]}>7/7</Text>
        </View>

        <View style={styles.innerContainer}>
          {/* TITLE */}
          <Text style={styles.title}>Eligibility Confirmation</Text>
          {/* DISCRIPTION */}
          <Text style={styles.discrption}>Add your degree and licenses </Text>
          {/* UPLOAD_YOUR_DEGREE */}
          <View style={{ marginTop: 18 }}>
            <Text style={styles.label}>Upload your Degree</Text>
            <Text style={styles.text}>Info will be added here</Text>
            <View style={styles.imageContainer}>
              {degreeImage?.path ? (
                <Image source={{ uri: degreeImage?.path }} style={{ width: "auto", height: 200 }} />
              ) : (
                <ButtonwithIcon icon={<Camera />} onPress={handleSelectDegree} />
              )}
            </View>
            {formik.touched.degree && <Text style={styles.errorText}>{formik.errors.degree}</Text>}
          </View>
          {/* UPLOAD_YOUR_LICENCES */}
          <View style={{ marginTop: 30 }}>
            <Text style={styles.label}>Upload your License</Text>
            <Text style={styles.text}>Info will be added here</Text>
            <View style={styles.imageContainer}>
              {licenseImage?.path ? (
                <Image source={{ uri: licenseImage?.path }} style={{ width: "auto", height: 200 }} />
              ) : (
                <ButtonwithIcon icon={<Camera />} onPress={handleSelectLicense} />
              )}
            </View>
            {formik.touched.license && <Text style={styles.errorText}>{formik.errors.license}</Text>}
          </View>
        </View>

        <View style={styles.footer}>
          <View>
            {/* SUBMIT_BUTTON */}
            <TextButton
              label={"Next"}
              containerStyle={styles.submitButton}
              onPress={formik.handleSubmit}
              disabled={isLoading}
              isLoading={isLoading}
            />
            <Text style={styles.paragraph}>
              Lorem ipsum dolor sit amet consectetur. Eget varius est posuere augue cursus suspendisse. s
            </Text>
          </View>
          <View style={styles.skipContainer}>
            <Text style={styles.skipText}>Can i skip?</Text>
            <Text style={[styles.paragraph, { marginTop: 0 }]}>
              Lorem ipsum dolor sit amet consectetur. Nec morbi sed nunc lorem enim nam enim adipiscing est. Enim est
              nisi velit proin vel amet aliquam. Sit in cras duis sit nulla nulla nunc.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileVerificationStep7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  innerContainer: {
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  skip: {
    backgroundColor: undefined,
    width: 40,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  label: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  text: {
    fontSize: 10,
    color: "#8A8A8A",
    marginTop: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  imageContainer: {
    backgroundColor: "#F9F9F9",
    marginTop: 10,
    paddingVertical: 22,
  },
  submitButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    width: "100%",
    marginTop: 40,
  },
  paragraph: {
    fontSize: 12,
    color: "#AAAAAA",
    textAlign: "center",
    marginTop: 16,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
    justifyContent: "space-between",
    paddingBottom: 22,
  },
  skipContainer: {
    backgroundColor: "#F9F9F9",
    paddingVertical: 12,
    paddingHorizontal: 18,
    borderRadius: 12,
    marginTop: 75,
  },
  skipText: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  errorText: {
    fontSize: 12,
    color: COLORS.red,
    paddingTop: 5,
    paddingLeft: 5,
  },
});
