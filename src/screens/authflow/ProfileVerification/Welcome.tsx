import { Image, SafeAreaView, StyleSheet, Text, View } from "react-native";
import React from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { Logo, WelcomePlant, WelcomImage } from "../../../assets/images";
import { TextButton } from "../../../components";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";

type Props = NativeStackScreenProps<AuthStackParamList, "Welcome">;

const Welcome: React.FC<Props> = ({ navigation, route }) => {
  return (
    <SafeAreaView style={styles.container}>
      {/* LOGO */}
      <Image resizeMode="contain" source={Logo} style={styles.imageContainer} />
      {/* LINE */}
      <View style={{ flexDirection: "row", marginTop: 22 }}>
        <View style={styles.line} />
        <View style={[styles.line, { backgroundColor: "blue" }]} />
      </View>
      {/* FLOWERS */}
      <View style={styles.flowerContain}>
        <Image resizeMode="contain" source={WelcomImage} style={{ width: "100%", height: "100%" }} />
      </View>
      <View style={styles.innerContainer}>
        <View>
          {/* HEADING */}
          <Text style={styles.heading}>Welcome!!</Text>
          {/* PARAGRAPH */}
          <Text style={styles.paragraph}>
            Lorem ipsum dolor sit amet consectetur. Ut risus non tellus cras lectus bibendum.
          </Text>
        </View>
        <View style={styles.footer}>
          {/* PARAGRAPH */}
          <Text style={[styles.paragraph, { color: "#EB4E1F", paddingHorizontal: 28 }]}>
            To start providing your services, you need to complete your profile
          </Text>
          {/* COMPLETED_BUTTON */}
          <TextButton
            label={"Complete My Profile"}
            containerStyle={styles.completedButton}
            onPress={() =>
              navigation.navigate(
                "ProfileVerificationStep1",
                //("ProfileVerificationStep2",
                {
                  userId: route.params.userId,
                },
              )
            }
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop + 10,
  },
  imageContainer: {
    width: 200,
    height: 45,
    alignSelf: "center",
    marginTop: 10,
  },
  line: {
    height: 1,
    backgroundColor: "red",
    flex: 1,
  },
  flowerContain: {
    height: 200,
    marginHorizontal: 20,
    marginTop: 70,
  },
  innerContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: 45,
  },
  heading: {
    fontSize: 16,
    color: COLORS.black,
    textAlign: "center",
    fontFamily: FONTS.Nunito_Bold,
    paddingTop: 26,
  },
  paragraph: {
    fontSize: 12,
    color: COLORS.black,
    textAlign: "center",
    marginTop: 8,
    fontFamily: FONTS.Nunito_Regular,
    paddingHorizontal: 38,
  },
  footer: {
    // backgroundColor: '#F9F9F9',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    width: "100%",
    paddingHorizontal: 20,
  },
  completedButton: {
    height: 52,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    marginTop: 30,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,

    elevation: 4,
  },
});
