import { SafeAreaView, StyleSheet, Text, View, ScrollView } from "react-native";
import React, { useMemo, useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import AuthServices from "../../../Services/AuthServices";
import { AxiosError } from "axios";
import { resetOfferServicesAction } from "../../../redux/slices/OfferServicesSlice";
import { updateUser } from "../../../redux/slices/AuthSlice";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import useApiHandler from "../../../Hooks/useApiHandler";

type Props = NativeStackScreenProps<AuthStackParamList & ProfessionalStackParamList, "ProfileVerificationStep6">;

const ProfileVerificationStep6: React.FC<Props> = ({ navigation, route }) => {
  const [isLoading, setLoading] = useState(false);
  const { results } = useAppSelector((state) => state.offerServices);
  const dispatch = useAppDispatch();
  const { handleApiErrors } = useApiHandler();

  const { userId, isProfile } = route.params;

  async function onSaveServices() {
    setLoading(true);
    await AuthServices.UpdateUser(userId, { data: { services: results } })
      .then((response) => {
        if (isProfile) {
          navigation.goBack();
          dispatch(updateUser({ services: results }));
        } else {
          navigation.navigate("ProfileVerificationStep7", { userId });
        }
        dispatch(resetOfferServicesAction());
      })
      .catch((error: AxiosError) => {
        handleApiErrors(error);
      });
    setLoading(false);
  }

  const services = useMemo(() => {
    const isArr = Object.prototype.toString.call(results) == "[object Array]";
    return isArr ? results : [];
  }, [results]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {!isProfile ? (
          <View style={styles.innerContainer}>
            <View style={styles.header}>
              {/* HEADING */}
              <Text style={styles.heading}>Complete My Profile</Text>
              {/* SKIP */}
              <TextButton
                label={"SKIP"}
                labelStyle={{
                  color: COLORS.black,
                  fontFamily: FONTS.Nunito_Regular,
                }}
                containerStyle={styles.skip}
                onPress={() =>
                  navigation.navigate("ProfileVerificationStep7", {
                    userId: route.params.userId,
                  })
                }
              />
            </View>
            <Text style={[styles.heading, { marginTop: 3 }]}>6/7</Text>
          </View>
        ) : null}
        <View style={styles.innerContainer}>
          {/* COUNTER */}

          {/* TITLE */}
          <Text style={styles.title}>Add services you offer</Text>
          {/* DISCRIPTION */}
          <Text style={styles.discrption}>Let patient select what exactly they needs</Text>

          {/* SERVICESDETAILS */}
          {services?.map((item, i) => (
            <View style={styles.serviceDetails} key={i}>
              <Text style={styles.serviceLabel}>{item.name}</Text>
              <Text style={styles.serviceTime}>1 hour @ ${item.price}</Text>
            </View>
          ))}
        </View>
        <View style={styles.footer}>
          {/* ADD_MORE */}
          <TextButton
            label={"Add More +"}
            labelStyle={{ color: COLORS.primary }}
            containerStyle={styles.addmore}
            onPress={() => {
              if (isProfile) {
                console.log("profile >>", isProfile);
                navigation.navigate("AddNewService", {
                  userId,
                  isProfile,
                });
              } else {
                navigation.goBack();
              }
            }}
          />
          {/* NEXT_BUTTON */}
          <TextButton
            label={isProfile ? "Save" : "Next"}
            containerStyle={styles.nextButton}
            onPress={onSaveServices}
            isLoading={isLoading}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileVerificationStep6;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop + 5,
  },
  innerContainer: {
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  skip: {
    backgroundColor: undefined,
    width: 40,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  serviceDetails: {
    backgroundColor: "#FBFBFB",
    paddingVertical: 20,
    paddingHorizontal: 24,
    marginBottom: 22,
    borderRadius: 12,
  },
  serviceLabel: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  serviceTime: {
    fontSize: 12,
    color: "#404040",
    marginTop: 8,
    fontFamily: FONTS.Nunito_Regular,
  },
  addmore: {
    backgroundColor: undefined,
    width: 90,
    alignSelf: "flex-end",
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    width: "100%",
    marginTop: 40,
  },
  footer: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: COLORS.white,
    paddingTop: 12,
    justifyContent: "space-between",
    paddingBottom: 45,
  },
});
