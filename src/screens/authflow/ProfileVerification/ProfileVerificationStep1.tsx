import { Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { Camera } from "../../../assets/svgicons";
import { TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { AxiosError } from "axios";
import AuthServices from "../../../Services/AuthServices";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { updateUser } from "../../../redux/slices/AuthSlice";
import ImageCropPicker, { ImageOrVideo } from "react-native-image-crop-picker";
import { UploadedImageResponse } from "../../../models/User";
import useApiHandler from "../../../Hooks/useApiHandler";
type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep1">;

const ProfileVerificationStep1: React.FC<Props> = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { userId, isProfile } = route.params;
  const [image, setImage] = useState<ImageOrVideo>();
  const { handleApiErrors } = useApiHandler();

  async function handleSelectDegree() {
    const image_data = await ImageCropPicker.openPicker({
      width: 300,
      height: 300,
      cropping: true,
      mediaType: "photo",
    });
    setImage(image_data);
  }
  const hardCodedImage =
    "https://hips.hearstapps.com/hmg-prod/images/portrait-of-a-happy-young-doctor-in-his-clinic-royalty-free-image-**********.jpg?crop=0.66698xw:1xh;center,top&resize=1200:*"; // Replace with your default image URL
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const updateUserPicture = async () => {
    try {
      setIsLoading(true);

      const image_formdata = new FormData();
      image_formdata.append("file", {
        uri: image?.path,
        type: image?.mime,
        name: userId + "_profile",
      });

      // const image_upload: UploadedImageResponse = (await AuthServices.UploadPicture(image_formdata)).data;

      await AuthServices.UpdateUser(userId, {
        data: {
          photo: hardCodedImage,
        },
      });

      if (isProfile) {
        navigation.goBack();
        dispatch(updateUser({ photo: hardCodedImage }));
      } else {
        navigation.navigate("ProfileVerificationStep2", { userId: userId });
        dispatch(updateUser({ photo: hardCodedImage }));
      }
    } catch (error) {
      handleApiErrors(error);
    } finally {
      setIsLoading(false);
    }
  };

  function renderImage() {
    if (image?.path) {
      return image.path;
    } else if (isProfile && user?.photo) {
      return user?.photo;
    } else {
      return "";
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {!isProfile && (
          <>
            <Text style={styles.heading}>Complete My Profile</Text>
            <Text style={[styles.heading, { marginTop: 3 }]}>1/7</Text>
          </>
        )}

        <Text style={styles.title}>Add a Profile Image</Text>

        <Text style={styles.paragraph}>Adding image will help more personalization</Text>
      </View>
      {/* ADD_PROFILE_IMAGE */}
      <View style={styles.imageContainer}>
        <TouchableOpacity onPress={handleSelectDegree} style={styles.iconContainer}>
          {renderImage() ? (
            <Image source={{ uri: renderImage() }} style={{ width: 200, height: 200, borderRadius: 100 }} />
          ) : (
            <>
              <Camera />
              <Text style={{ color: "#606060" }}>Add Image +</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      {/*FOOTER  */}
      <View style={styles.footer}>
        {/* DISCRIPTION */}
        <Text style={styles.discription}>Your image should be Front Facing and of High Quality</Text>
        {/* NEXT_BUTTON */}
        <TextButton
          label={isProfile ? "Save" : "Next"}
          containerStyle={styles.nextButton}
          onPress={updateUserPicture}
          isLoading={isLoading}
          disabled={isLoading}
        />
      </View>
    </SafeAreaView>
  );
};

export default ProfileVerificationStep1;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop + 10,
  },
  header: {
    marginTop: 5,
    paddingHorizontal: 20,
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    fontSize: 14,
    color: "#606060",
    marginTop: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  imageContainer: {
    backgroundColor: "#F9F9F9",
    height: 250,
    marginHorizontal: 20,
    marginTop: 23,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainer: {
    alignItems: "center",
  },
  footer: {
    flex: 1,
    alignItems: "center",
    marginTop: 18,
    justifyContent: "space-between",
    paddingBottom: 45,
  },
  discription: {
    fontSize: 12,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Regular,
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.red,
    width: "89%",
  },
});
