import {
  Layout<PERSON>nimation,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { ChooseTime, TextButton, TextButtonwithIcon } from "../../../components";
import { WhiteCheck } from "../../../assets/svgicons";
import { time } from "../../../constants";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";

import { useFormik } from "formik";
import * as Yup from "yup";
import { AxiosError } from "axios";
import AuthServices from "../../../Services/AuthServices";
import moment from "moment";
import useApiHandler from "../../../Hooks/useApiHandler";

type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep4">;

const daysOfWeek = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

const ProfileVerificationStep4: React.FC<Props> = ({ navigation, route }) => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [showAdvanceSchedule, setShowAdvanceSchedule] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDay, setSelectedDay] = useState<string>("");
  const { handleApiErrors } = useApiHandler();

  const { userId } = route.params;

  const [isFromTime, setIsFromTime] = useState(true);

  const initialValues = {
    schedule: daysOfWeek.map((item) => ({ day: item, from: "", to: "" })),
  };

  const validationSchema = Yup.object().shape({
    schedule: Yup.array()
      .of(
        Yup.object().shape({
          day: Yup.string().required(),
          from: Yup.string()
            .required("Start time is required")
            .test("is-valid-time", "Invalid time format", (value) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(value)),
          to: Yup.string()
            .required("End time is required")
            .test("is-valid-time", "Invalid time format", (value) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(value))
            .test("is-after-from", "End time must be after start time", function (value) {
              const { from } = this.parent;
              return (
                from && value && new Date(`1970-01-01T${value}`).getTime() > new Date(`1970-01-01T${from}`).getTime()
              );
            }),
        }),
      )
      .test("all-days-set", "Please provide times for all days", function (schedule) {
        return schedule && schedule.length >= 1 && schedule.every((day) => day.from && day.to);
      }),
  });

  const { values, errors, touched, setFieldValue, isValid, handleSubmit } = useFormik({
    initialValues,
    validationSchema,
    validateOnMount: true,
    onSubmit: async () => {
      try {
        setIsLoading(true);

        const resp = await AuthServices.UpdateUser(userId, {
          data: {
            schedule: values.schedule,
            isOnboarded: true,
          },
        });
        if (resp.status === 200) {
          console.log(resp.data);
          navigation.navigate("ProfileVerificationStep5", { userId: userId });
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  const showDatePicker = (isFrom: boolean) => {
    setIsFromTime(isFrom);
    setDatePickerVisible(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisible(false);
  };

  const handleConfirm = (date: Date) => {
    const formattedTime = moment(date).format("HH:mm");

    if (!showAdvanceSchedule) {
      // General schedule for all days
      const updatedSchedule = daysOfWeek.map((day) => ({
        day: day,
        from: isFromTime ? formattedTime : values.schedule.find((d) => d.day === day)?.from || "",
        to: !isFromTime ? formattedTime : values.schedule.find((d) => d.day === day)?.to || "",
      }));

      setFieldValue("schedule", updatedSchedule);
    } else {
      // Advanced schedule (individual days)
      let updatedSchedule = [...values.schedule];
      console.log("selected.day >> ", selectedDay);
      const dayIndex = updatedSchedule.findIndex((item) => item.day === selectedDay); // Find the specific day being updated

      if (dayIndex >= 0) {
        // Update 'from' or 'to' value for the specific day
        updatedSchedule[dayIndex][isFromTime ? "from" : "to"] = formattedTime;
      } else {
      }

      setFieldValue("schedule", updatedSchedule);
    }

    hideDatePicker();
  };

  const handleWorkingDaysOfWeek = (day: string) => {
    let alreadyAdded = values.schedule.find((item) => item.day === day);
    if (alreadyAdded) {
      let newArr = values.schedule.filter((item) => item.day !== day);
      setFieldValue("schedule", newArr);
    } else {
      let payload = {
        day,
        from: "",
        to: "",
      };
      setFieldValue("schedule", [...values.schedule, payload]);
    }
  };

  useEffect(() => {
    setFieldValue("schedule", []);
  }, [showAdvanceSchedule]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.innerContainer}>
          <Text style={styles.heading}>Complete My Profile</Text>
          <Text style={[styles.heading, { marginTop: 3 }]}>4/7</Text>
          <Text style={styles.title}>Update Schedule</Text>
          <Text style={styles.discrption}>Manage your availability </Text>
          <Text style={styles.generalText}>Select general availability to receive bookings</Text>
          <Text style={[styles.text, { marginTop: 18 }]}>Select Days</Text>
          <Text style={[styles.text, { marginTop: 7 }]}>All Days</Text>
          <Text style={styles.availableText}>*available in advance</Text>

          {/* SESSION_TIME */}
          <Text style={styles.selectTimeText}>Select Time</Text>
          <View style={{ flexDirection: "row", marginTop: 8 }}>
            {/* FROM */}
            <TouchableOpacity style={styles.time} onPress={() => showDatePicker(true)}>
              <Text style={{ color: "#606060" }}>{(!showAdvanceSchedule && values.schedule?.[0]?.from) || "From"}</Text>
            </TouchableOpacity>
            {/* TO */}
            <TouchableOpacity style={[styles.time, { marginLeft: 20 }]} onPress={() => showDatePicker(false)}>
              <Text style={{ color: "#606060" }}>{(!showAdvanceSchedule && values.schedule?.[0]?.to) || "To"}</Text>
            </TouchableOpacity>
          </View>

          {touched?.[0]?.from && errors?.[0]?.from ? (
            <Text style={{ color: "red", fontSize: 12, marginTop: 5 }}>{errors?.[0]?.from}</Text>
          ) : null}

          {touched?.[0]?.to && errors?.[0]?.to ? (
            <Text style={{ color: "red", fontSize: 12, marginTop: 5 }}>{errors?.[0]?.to}</Text>
          ) : null}
        </View>

        {/* BUTTON */}
        <TextButtonwithIcon
          label={"Advance Schedule"}
          labelStyle={{ fontSize: 14, fontFamily: FONTS.Nunito_SemiBold }}
          leftIcon={<WhiteCheck />}
          leftIconContainer={
            [styles.icon, { backgroundColor: showAdvanceSchedule ? COLORS.red : "#D9D9D9" }] as ViewStyle
          }
          containerStyle={{ marginTop: 15 }}
          onPress={() => setShowAdvanceSchedule(!showAdvanceSchedule)}
        />

        {showAdvanceSchedule ? (
          <View
            style={{
              paddingHorizontal: 20,
              paddingVertical: 15,
            }}
          >
            <View>
              <View style={{ flexDirection: "row", marginTop: 0 }}>
                <Text style={[styles.subTitle, { flex: 1 }]}>Select Days</Text>
                <Text style={[styles.subTitle, { flex: 0.7, paddingLeft: 30 }]}>From</Text>
                <Text style={[styles.subTitle, { flex: 0.3 }]}>To</Text>
              </View>
              {daysOfWeek.map((item) => (
                <View key={item} style={{ marginTop: 16 }}>
                  <ChooseTime
                    day={item}
                    isSelected={!!values.schedule.find((x) => x.day === item)}
                    onSelect={() => handleWorkingDaysOfWeek(item)}
                    from={values.schedule.find((v) => v.day === item)?.from}
                    to={values.schedule.find((v) => v.day === item)?.to}
                    containerStyle={{ marginBottom: 13 }}
                    onToPress={() => {
                      setSelectedDay(item);
                      showDatePicker(false);
                    }}
                    onFromPress={() => {
                      setSelectedDay(item);
                      showDatePicker(true);
                    }}
                  />
                </View>
              ))}
            </View>
          </View>
        ) : null}
        <TextButton
          label={"Next"}
          containerStyle={styles.nextButton}
          onPress={handleSubmit}
          isLoading={isLoading}
          disabled={isLoading || !isValid}
        />
      </ScrollView>

      <DateTimePickerModal
        date={selectedDate}
        isVisible={datePickerVisible}
        mode="time"
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
        minuteInterval={30}
      />
    </SafeAreaView>
  );
};

export default ProfileVerificationStep4;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  innerContainer: {
    marginTop: 5,
    paddingHorizontal: 20,
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  generalText: {
    fontSize: 14,
    color: COLORS.black,
    marginTop: 23,
    fontFamily: FONTS.Nunito_Bold,
  },
  text: {
    fontSize: 14,
    color: "#292D3280",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  availableText: {
    fontSize: 10,
    color: "#00000070",
    marginTop: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  selectTimeText: {
    marginTop: 17,
    fontSize: 14,
    fontFamily: FONTS.Nunito_SemiBold,
    color: "#292D32",
  },
  time: {
    width: 106,
    height: 50,
    backgroundColor: "#F8F8F8",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ECECEC",
    justifyContent: "center",
    paddingLeft: 16,
    color: "#00000070",
  },
  icon: {
    width: 18,
    height: 18,
    borderRadius: 5,
    marginRight: 4,
    marginLeft: 20,
  },
  subTitle: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    marginVertical: 32,
    marginHorizontal: 24,
  },
});
