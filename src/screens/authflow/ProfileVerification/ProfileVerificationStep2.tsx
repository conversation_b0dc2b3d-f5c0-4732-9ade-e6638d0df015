import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import FormInput from "../../../components/FormInput";
import AuthServices from "../../../Services/AuthServices";
import { AxiosError } from "axios";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import { updateUser } from "../../../redux/slices/AuthSlice";
import useApiHandler from "../../../Hooks/useApiHandler";
type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep2">;

const ProfileVerificationStep2: React.FC<Props> = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const { handleApiErrors } = useApiHandler();

  const { userId, isProfile } = route.params;

  const validationSchema = Yup.object().shape({
    description: Yup.string().required("Description is required"),
  });

  const { handleChange, handleBlur, handleSubmit, setFieldValue, values, errors, touched, isValid } = useFormik({
    initialValues: { description: "" },
    validationSchema,
    validateOnMount: true,
    onSubmit: async () => {
      setIsLoading(true);
      await AuthServices.UpdateUser(userId, {
        data: { introduction: values.description },
      })
        .then(() => {
          if (isProfile) {
            navigation.goBack();
            dispatch(updateUser({ introduction: values.description }));
            return;
          } else {
            navigation.navigate("ProfileVerificationStep3", { userId: userId });
            dispatch(updateUser({ introduction: values.description }));
          }
        })
        .catch((error) => {
          handleApiErrors(error);
        })
        .finally(() => setIsLoading(false));
    },
  });

  useEffect(() => {
    if (isProfile) {
      setFieldValue("description", user?.introduction);
    }
  }, []);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <SafeAreaView style={styles.innerContainer}>
        <View style={styles.header}>
          {!isProfile && <Text style={styles.heading}>Complete My Profile</Text>}
          {!isProfile && <Text style={[styles.heading, { marginTop: 3 }]}>2/7</Text>}
          <Text style={styles.title}>Tell us a bit</Text>
          <Text style={[styles.title, { marginTop: 0 }]}>about yourself & Work</Text>
          <Text style={styles.discrption}>Edit description </Text>

          <FormInput
            errorMessage={touched.description && errors.description ? errors.description : ""}
            multiline
            style={{
              height: 260,
              flex: 1,
              alignSelf: "flex-start",
              // marginTop: 0,
              color: "#000",
              textAlignVertical: "top",
              padding: 20,
            }}
            placeholderTextColor={"#828282"}
            containerStyle={{
              height: 200,
              borderWidth: 0,
              backgroundColor: "#F9F9F9",
              paddingHorizontal: 0,
            }}
            placeholder="Description"
            onChangeText={handleChange("description")}
            onBlur={handleBlur("description")}
            value={values.description}
            returnKeyType="default"
            keyboardType="default"
            blurOnSubmit={false}
          />
        </View>
        <View style={styles.footer}>
          <TextButton
            label={isProfile ? "Save" : "Next"}
            containerStyle={styles.nextButton}
            onPress={handleSubmit}
            isLoading={isLoading}
            disabled={isLoading || !isValid}
          />
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default ProfileVerificationStep2;

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  innerContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    marginTop: 5,
    paddingHorizontal: 20,
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 23,
    fontFamily: FONTS.Nunito_Regular,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.black,
    marginTop: 3,
    fontFamily: FONTS.Nunito_Medium,
  },
  footer: {
    flex: 1,
    justifyContent: "flex-end",
    marginHorizontal: 20,
    paddingBottom: 45,
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    marginVertical: 16,
  },
});
