import {
  Keyboard,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useState, useEffect } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { TextButton, TextInputCmp } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useFormik } from "formik";
import * as Yup from "yup";
import { AxiosError } from "axios";
import AuthServices from "../../../Services/AuthServices";
import useApiHandler from "../../../Hooks/useApiHandler";
import { getCategoriesForBodyParts2 } from "../../../Services/CategoriesService";
type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep3">;

const ProfileVerificationStep3: React.FC<Props> = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { userId } = route.params;
  const { handleApiErrors } = useApiHandler();
  const [bodyParts, setBodyParts] = useState<Array<{ id: number; label: string; isSelected: boolean }>>([]);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categories = await getCategoriesForBodyParts2();
        setBodyParts(categories);
      } catch (error) {
        console.error("Error loading categories:", error);
      }
    };
    loadCategories();
  }, []);

  const toggleBodyPart = (label: string) => {
    const selected = values.bodyParts.includes(label);
    if (selected) {
      setFieldValue(
        "bodyParts",
        values.bodyParts.filter((part) => part !== label),
      );
    } else {
      setFieldValue("bodyParts", [...values.bodyParts, label]);
    }
  };

  const validationSchema = Yup.object().shape({
    yearsOfExp: Yup.number()
      .typeError("Please enter a valid number")
      .min(1, "Must be at least 1 year")
      .required("Years of Experience is required"),
    avgPatients: Yup.number()
      .typeError("Please enter a valid number")
      .min(1, "Must be at least 1")
      .required("Average No. of patients is required"),
    bodyParts: Yup.array()
      .of(Yup.string())
      .min(1, "Please select at least one body part")
      .required("Specialization in pain relief is required"),
  });

  const { handleChange, handleBlur, handleSubmit, values, errors, touched, setFieldValue, isValid } = useFormik({
    initialValues: { yearsOfExp: "", avgPatients: "", bodyParts: [] },
    validationSchema,
    validateOnMount: true,
    onSubmit: async () => {
      try {
        setIsLoading(true);
        const resp = await AuthServices.UpdateUser(userId, {
          data: {
            workExperience: {
              patients: values.avgPatients,
              years: values.yearsOfExp,
              specialization: values.bodyParts,
            },
          },
        });
        if (resp.status == 200) {
          console.log(resp.data);
          navigation.navigate("ProfileVerificationStep4", { userId: userId });
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });
  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <ScrollView contentContainerStyle={{ backgroundColor: COLORS.white, flexGrow: 1 }}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            {/* HEADING */}
            <Text style={styles.heading}>Complete My Profile</Text>
            {/* COUNTER */}
            <Text style={[styles.heading, { marginTop: 3 }]}>3/7</Text>
            {/* TITLE */}
            <Text style={styles.title}>Work Experience</Text>
            {/* DISCRIPTION */}
            <Text style={styles.discrption}>It is always beneficial to know to gain more trust</Text>
          </View>
          {/* INPUT_FIELDS */}
          <View style={{ marginHorizontal: 20, marginTop: 50 }}>
            <TextInputCmp
              label={"Years of Experience"}
              maxLength={2}
              placeholder={"Write here"}
              placeholderTextColor={"#606060"}
              onChangeText={handleChange("yearsOfExp")}
              onBlur={handleBlur("yearsOfExp")}
              value={values.yearsOfExp}
              keyboardType="numeric"
              errorMessage={touched.yearsOfExp && errors.yearsOfExp ? errors.yearsOfExp : ""}
            />
            <TextInputCmp
              label={"Average No. of Patients dealt with"}
              placeholder={"Average No. of Patients dealt with"}
              placeholderTextColor={"#606060"}
              containerStyle={{ marginTop: 30 }}
              onChangeText={handleChange("avgPatients")}
              onBlur={handleBlur("avgPatients")}
              value={values.avgPatients}
              keyboardType="numeric"
              errorMessage={touched.avgPatients && errors.avgPatients ? errors.avgPatients : ""}
            />
          </View>
          <View style={styles.footer}>
            <View>
              <Text style={styles.label}>
                Specialized in resolving pain for{" "}
                <Text style={{ fontFamily: FONTS.Nunito_Regular, color: "#707070" }}>(optional)</Text>
              </Text>
              <View style={styles.bodyContainer}>
                {bodyParts.map((item, index) => {
                  return (
                    <TouchableOpacity
                      key={`bodyParts-${index}`}
                      onPress={() => toggleBodyPart(item.label)}
                      style={[
                        styles.bodyPartsContainer,
                        {
                          backgroundColor: values.bodyParts.includes(item.label) ? "blue" : "#F5F5F5",
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.bodyLabel,
                          {
                            color: values.bodyParts.includes(item.label) ? "white" : "#707070",
                          },
                        ]}
                      >
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
                {touched.bodyParts && errors.bodyParts && (
                  <Text style={{ color: "red", fontSize: 12, marginTop: 5 }}>{JSON.stringify(errors.bodyParts)}</Text>
                )}
              </View>
            </View>
            <TextButton
              label={"Next"}
              containerStyle={styles.nextButton}
              onPress={handleSubmit}
              isLoading={isLoading}
              disabled={isLoading || !isValid}
            />
          </View>
        </SafeAreaView>
      </ScrollView>
    </TouchableWithoutFeedback>
  );
};

export default ProfileVerificationStep3;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  header: {
    marginTop: 5,
    paddingHorizontal: 20,
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    marginTop: 30,
    marginHorizontal: 20,
    justifyContent: "space-between",
    flex: 1,
    paddingBottom: 45,
  },
  label: {
    fontSize: 13,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  textInputStyles: {
    height: 51,
    borderRadius: 5,
    backgroundColor: "#F9F9F9",
    marginTop: 9,
    paddingLeft: 14,
  },
  bodyPartsContainer: {
    backgroundColor: "#F5F5F5",
    height: 42,
    paddingHorizontal: 20,
    marginRight: 15,
    marginBottom: 15,
    borderRadius: 42 / 2,
    alignItems: "center",
    justifyContent: "center",
  },
  bodyLabel: {
    fontSize: 12,
    color: "#707070",
  },
  bodyContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 15,
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    marginTop: 32,
  },
});
