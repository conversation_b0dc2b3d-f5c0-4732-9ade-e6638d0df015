import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";
import React from "react";
import { appStyles, COLORS, FONTS, sizes } from "../../../themes";
import { TextButton, TextInputCmp } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useAppDispatch } from "../../../redux/config/Store";
import { IOfferService } from "../../../models/IOfferService";
import { addOfferServiceAction } from "../../../redux/slices/OfferServicesSlice";
import { DropDown, Gender } from "../../../assets/svgicons";
import RNPickerSelect from "react-native-picker-select";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

type Props = NativeStackScreenProps<AuthStackParamList, "ProfileVerificationStep5">;

const validationSchema = Yup.object({
  name: Yup.string().required("Service Name is required"),
  sessionTime: Yup.number(),
  price: Yup.string().required("price is required"),
  description: Yup.string().optional(),
});

const ProfileVerificationStep5: React.FC<Props> = ({ navigation, route }) => {
  const dispatch = useAppDispatch();
  const { userId, isProfile } = route.params;

  const formik = useFormik<IOfferService>({
    initialValues: {
      name: "",
      sessionTime: 60, // it's 1 hour as 60 minutes
      price: "",
      description: "",
    },
    validationSchema,
    onSubmit: (values) => {
      dispatch(addOfferServiceAction(values as IOfferService));
      if (isProfile) {
        navigation.goBack();
      } else {
        navigation.navigate("ProfileVerificationStep6", {
          userId,
        });
      }
      formik.resetForm();
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.innerContainer}>
          {!isProfile && (
            <View style={styles.header}>
              {/* HEADING */}
              <Text style={styles.heading}>Complete My Profile</Text>
              {/* SKIP */}
              <TextButton
                label={"SKIP"}
                labelStyle={{
                  color: COLORS.black,
                  fontFamily: FONTS.Nunito_Regular,
                }}
                containerStyle={styles.skip}
                onPress={() =>
                  navigation.navigate("ProfileVerificationStep7", {
                    userId: route.params.userId,
                  })
                }
              />
            </View>
          )}
          {/* COUNTER */}
          {!isProfile && <Text style={[styles.heading, { marginTop: 3 }]}>5/7</Text>}

          {/* TITLE */}
          <Text style={styles.title}>Add services you offer</Text>
          {/* DISCRIPTION */}
          <Text style={styles.discrption}>Let patient select what exactly they need</Text>
          {/* SERVICES_NAME */}
          <TextInputCmp
            label={"Service Name"}
            placeholder={"Write here"}
            placeholderTextColor={"#606060"}
            value={formik.values.name}
            onChangeText={formik.handleChange("name")}
            onBlur={formik.handleBlur("name")}
            errorMessage={formik.touched.name && formik.errors.name}
          />

          {/* SESSION_TIME */}
          {/* <TextInputCmp
            label={'Session Time'}
            placeholder={'Select'}
            placeholderTextColor={'#606060'}
            containerStyle={{marginTop: 23}}
            value={formik.values.sessionTime}
            onChangeText={formik.handleChange('sessionTime')}
            onBlur={formik.handleBlur('sessionTime')}
            errorMessage={
              formik.touched.sessionTime && formik.errors.sessionTime
            }
          /> */}
          {/* <Text style={styles.label}>{"Session Time"}</Text>
          <View style={styles.pickerContainer}>
            <RNPickerSelect
              placeholder={{
                label: "Select",
                value: "",
                color: "#606060",
              }}
              onValueChange={(value) => {
                formik.setFieldValue("sessionTime", value);
              }}
              onClose={() => formik.handleBlur("sessionTime")}
              items={[
                { label: "30 minutes", value: "30 minutes" },
                { label: "45 minutes", value: "45 minutes" },
                { label: "1 hour", value: "1 hour" },
              ]}
              value={formik.values.sessionTime}
              useNativeAndroidPickerStyle={false}
              Icon={() => <DropDown />}
              style={{
                iconContainer: styles.iconContainer,
                inputAndroid: styles.inputAndroid,
                inputIOS: styles.inputIOS,
              }}
            />
          </View>
          {formik.touched.sessionTime && formik.errors.sessionTime && (
            <Text style={appStyles.errorText}>{formik.errors.sessionTime}</Text>
          )} */}

          {/* price */}
          <TextInputCmp
            label={"price"}
            placeholder={"$00"}
            placeholderTextColor={"#606060"}
            containerStyle={{ marginTop: 23 }}
            value={formik.values.price}
            onChangeText={formik.handleChange("price")}
            onBlur={formik.handleBlur("price")}
            keyboardType="numeric"
            errorMessage={formik.touched.price && formik.errors.price}
          />

          {/* DISCRIPTION */}
          <TextInputCmp
            label={"Description (optional)"}
            placeholder={"write about this session here"}
            placeholderTextColor={"#606060"}
            containerStyle={{ marginTop: 23 }}
            style={{
              height: 100,
              alignItems: "flex-start",
              padding: Platform.OS === "android" ? 0 : 15,
            }}
            multiline={true}
            value={formik.values.description}
            onChangeText={formik.handleChange("description")}
            onBlur={formik.handleBlur("description")}
            errorMessage={formik.touched.description && formik.errors.description}
            textAlignVertical="top"
          />
          {/* ADD_BUTTONS */}
          <TextButton
            label={"Add"}
            containerStyle={styles.addButton}
            onPress={formik.handleSubmit}
            disabled={!formik.isValid || !formik.dirty}
          />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ProfileVerificationStep5;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  innerContainer: {
    paddingHorizontal: 20,
    paddingTop: sizes.paddingTop + 7,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  heading: {
    fontSize: 16,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  skip: {
    backgroundColor: null,
    width: 40,
  },
  title: {
    fontSize: 24,
    color: "#030F1C",
    marginTop: 22,
    fontFamily: FONTS.Nunito_Bold,
  },
  discrption: {
    fontSize: 14,
    color: "#606060",
    marginVertical: 3,
    fontFamily: FONTS.Nunito_Regular,
  },
  addButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.primary,
    width: "100%",
    marginVertical: 40,
  },
  pickerContainer: {
    backgroundColor: "#F9F9F9",
    height: 55,
    borderRadius: 10,
    justifyContent: "center",
    marginTop: 0,
    paddingLeft: 15,
    paddingRight: 20,
  },
  inputAndroid: {
    color: "#000",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  inputIOS: {
    color: "#000",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  iconContainer: {
    top: Platform.OS === "ios" ? 5 : 22,
    right: 0,
  },
  label: {
    fontSize: 13,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    marginVertical: 12,
  },
});
