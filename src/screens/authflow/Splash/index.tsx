import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import {COLORS, FONTS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {OW_LOGO} from '../../../assets/svgicons';
import {useAppDispatch} from '../../../redux/config/Store';
import {setHasSelectedRole} from '../../../redux/slices/AuthSlice';
type Props = NativeStackScreenProps<AuthStackParamList, 'Splash'>;

const Splash: React.FC<Props> = ({navigation}) => {
  const dispatch = useAppDispatch();

  return (
    <SafeAreaView style={styles.container}>
      {/* LOGO */}
      <View style={styles.logoContaner}>
        <OW_LOGO style={{width: 280, height: 80}} />
      </View>
      <View style={styles.footer}>
        {/* TITLE */}
        <Text style={styles.title}>Continue as?</Text>
        {/* SUB_TITLE */}
        <Text style={styles.subTitle}>Select who you are</Text>
        <View style={{flexDirection: 'row', marginTop: 24}}>
          {/* THERAPIST */}
          <TouchableOpacity
            style={styles.therapistrd}
            activeOpacity={0.8}
            onPress={() => {
              navigation.replace('SignUp');
              dispatch(
                setHasSelectedRole({
                  selectedRole: 'doctor',
                  hasSelectedRole: true,
                }),
              );
            }}>
            <Text style={styles.text}>Therapist</Text>
          </TouchableOpacity>
          {/* PATIENT */}

          <TouchableOpacity
            style={styles.patient}
            activeOpacity={0.8}
            onPress={() => {
              navigation.replace('SignUp');
              dispatch(
                setHasSelectedRole({
                  selectedRole: 'patient',
                  hasSelectedRole: true,
                }),
              );
            }}>
            <Text style={{...styles.text, marginTop: 30}}>Patient</Text>
            <Text style={styles.cancleButton}>Client</Text>
            {/* <TextButton
              label={'Client'}
              labelStyle={{fontSize: 13, fontFamily: FONTS.Nunito_Light}}
              containerStyle={styles.cancleButton}
            /> */}
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Splash;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  logoContaner: {
    flex: 0.7,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    backgroundColor: '#FEF4E8',
    flex: 0.35,
    paddingHorizontal: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  title: {
    fontSize: 20,
    color: COLORS.black,
    marginTop: 25,
    fontFamily: FONTS.Nunito_Bold,
  },
  subTitle: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  therapistrd: {
    borderRadius: 12,
    backgroundColor: '#D12A83',
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginRight: 6,
  },
  patient: {
    borderRadius: 12,
    backgroundColor: '#EB4E1F',
    height: 120,
    alignItems: 'center',
    flex: 1,
    marginLeft: 6,
  },
  text: {
    fontSize: 16,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  cancleButton: {
    fontFamily: FONTS.Nunito_SemiBold,
    backgroundColor: '#FF6436',
    paddingHorizontal: 17,
    paddingVertical: 2,
    borderRadius: 6,
    marginTop: 30,
    color: '#fff',
  },
});
