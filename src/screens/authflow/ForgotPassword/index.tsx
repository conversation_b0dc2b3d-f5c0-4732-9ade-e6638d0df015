import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useState } from "react";
import { Forgotpassword } from "../../../assets/images";
import { COLORS, FONTS } from "../../../themes";
import { Forminput, TextButton } from "../../../components";
import { Email } from "../../../assets/svgicons";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../../navigation/AuthStack";
import { AxiosError } from "axios";
import AuthServices from "../../../Services/AuthServices";
import { useFormik } from "formik";
import * as Yup from "yup";
import useApiHandler from "../../../Hooks/useApiHandler";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
type Props = NativeStackScreenProps<AuthStackParamList, "ForgotPassword">;

const validationSchema = Yup.object().shape({
  email: Yup.string().email("Invalid email").required("Email is required"),
});
const ForgotPassword: React.FC<Props> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { handleApiErrors } = useApiHandler();

  const formik = useFormik({
    initialValues: { email: "" },
    validationSchema,
    validateOnMount: true,
    onSubmit: async (values) => {
      setIsLoading(true);
      await AuthServices.ForgotPassword(values.email)
        .then(() => {
          navigation.navigate("VerifyOTP", { email: values.email });
        })
        .catch((error: AxiosError) => {
          handleApiErrors(error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    },
  });

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <SafeAreaView style={styles.container}>
          {/* BACKGROUND_IMAGE */}
          <Image source={Forgotpassword} style={styles.image} />
          {/* TITLE */}
          <Text style={styles.title}>Forgot Password</Text>
          {/* PARAGRAPH */}
          <Text style={styles.paragraph}>
            Enter your email or phone no. so that we can send you an OTP code to reset your password
          </Text>
          <View style={styles.footer}>
            {/* EMAIL INPUT FIELD */}
            <Forminput
              placeholder="Email"
              placeholderTextColor="#292D3260"
              leftIcon={<Email />}
              containerStyle={{ marginTop: 12 }}
              value={formik.values.email}
              onChangeText={formik.handleChange("email")}
              onBlur={formik.handleBlur("email")}
              keyboardType="email-address"
              autoCapitalize="none"
              errorMessage={formik.touched.email && formik.errors.email ? formik.errors.email : ""}
            />
            {/* CONTINUE BUTTON */}
            <TextButton
              label={"Continue"}
              containerStyle={styles.button}
              onPress={formik.handleSubmit}
              labelStyle={{ fontSize: 15 }}
              disabled={!formik.isValid || isLoading}
              isLoading={isLoading}
            />
          </View>
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </TouchableWithoutFeedback>
  );
};

export default ForgotPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  image: {
    width: 300,
    height: 332,
    alignSelf: "center",
    resizeMode: "contain",
  },
  title: {
    fontSize: 24,
    color: COLORS.black,
    textAlign: "center",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.black,
    marginHorizontal: 30,
    textAlign: "center",
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    marginHorizontal: 20,
    flex: 1,
    marginTop: 18,
    justifyContent: "space-between",
  },
  button: {
    backgroundColor: COLORS.primary,
    height: 55,
    borderRadius: 8,
    marginVertical: 40,
  },
});
