import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {OTP} from '../../../assets/images';
import {COLORS, FONTS} from '../../../themes';
import {TextButton} from '../../../components';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthStack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import AuthServices from '../../../Services/AuthServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import useApiHandler from '../../../Hooks/useApiHandler';

type Props = NativeStackScreenProps<AuthStackParamList, 'VerifyOTP'>;

const VerifyOTP: React.FC<Props> = ({navigation, route}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const {handleApiErrors} = useApiHandler();

  const {email} = route.params;

  useEffect(() => {
    let countdown;
    if (timer > 0) {
      countdown = setInterval(() => {
        setTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(countdown);
  }, [timer]);

  const formik = useFormik({
    initialValues: {otp: ''},
    validationSchema: Yup.object({
      otp: Yup.string()
        .length(4, 'OTP must be exactly 4 digits')
        .required('OTP is required'),
    }),
    validateOnMount: true,
    onSubmit: async values => {
      try {
        setIsLoading(true);
        const resp = await AuthServices.VerifyOtp(email, values.otp);
        if (resp.status == 201) {
          navigation.navigate('CreatePassword', {email});
        }
      } catch (error) {
        handleApiErrors(error);
      } finally {
        setIsLoading(false);
      }
    },
  });

  const handleResend = async () => {
    try {
      if (timer === 0) {
        const resp = await AuthServices.ForgotPassword(email);
        if (resp.status == 200) {
          Toast.show({
            type: 'info',
            text1: 'OTP sent!',
            text2: 'OTP sent to your email.',
          });
          setTimer(60);
        }
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error resending otp', err);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={{flexGrow: 1}}
        showsVerticalScrollIndicator={false}>
        {/* BACKGROUND_IMAGE */}
        <Image source={OTP} style={styles.image} />
        {/* TITLE */}
        <Text style={styles.title}>OTP VERIFICATION</Text>
        {/* PARAGRAPH */}
        <Text style={styles.paragraph}>
          Enter otp sent to your email for the verification process.
        </Text>
        <View style={styles.footer}>
          {/* EMAI_INPUT_FEILDS */}

          <OTPInputView
            pinCount={4}
            style={styles.btnCotainer}
            autoFocusOnLoad={false}
            codeInputFieldStyle={styles.underlineStyleBase}
            onCodeFilled={code => formik.setFieldValue('otp', code)}
            placeholderCharacter="0"
            selectionColor={COLORS.white}
            placeholderTextColor="#828282"
          />
          {/* Error message display */}
          {formik.errors.otp && formik.touched.otp && (
            <Text style={styles.errorText}>{formik.errors.otp}</Text>
          )}
          <View>
            {/* CONTINUE_BUTTON */}
            <TextButton
              label={'Submit'}
              containerStyle={styles.button}
              onPress={formik.handleSubmit}
              isLoading={isLoading}
              disabled={isLoading || !formik.isValid}
            />
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <Text style={styles.codeText}>Don’t receive code?</Text>
              <TextButton
                label={timer > 0 ? `Re-send in ${timer}s` : 'Re-send'}
                labelStyle={
                  timer > 0 ? styles.resendDisabled : styles.resendBtnText
                }
                onPress={handleResend}
                containerStyle={{backgroundColor: 'white'}}
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default VerifyOTP;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  image: {
    width: 300,
    height: 332,
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  title: {
    fontSize: 24,
    color: COLORS.black,
    textAlign: 'center',
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.black,
    paddingHorizontal: 50,
    textAlign: 'center',
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    marginHorizontal: 20,
    flex: 1,
    marginTop: 18,
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  button: {
    backgroundColor: COLORS.primary,
    height: 55,
    borderRadius: 8,
    marginVertical: 16,
  },
  underlineStyleBase: {
    width: 65,
    height: 52,
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: '#F9F9F9',
    color: '#828282',
  },
  btnCotainer: {
    height: 52,
    marginTop: 20,
  },
  codeText: {
    color: '#828282',
    fontSize: 15,
    fontFamily: FONTS.Nunito_Regular,
  },
  resendBtnText: {
    color: COLORS.primary,
    marginLeft: 7,
    fontFamily: FONTS.Nunito_Bold,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginTop: 8,
  },
  resendDisabled: {
    color: COLORS.borderColor,
    marginLeft: 7,
    fontFamily: FONTS.Nunito_Bold,
  },
});
