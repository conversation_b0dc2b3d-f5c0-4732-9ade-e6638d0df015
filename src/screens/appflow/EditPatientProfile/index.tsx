import { <PERSON>Background, SafeAreaView, ScrollView, StyleSheet, Text, View } from "react-native";
import React from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { ButtonwithIcon, TextButtonwithIcon } from "../../../components";
import { ArrowLeft, Edit3, Location } from "../../../assets/svgicons";
import { EMPTY_AVATAR } from "../../../assets/images";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import { HomeStackParamList } from "../../../navigation/HomeStack";

type Props = NativeStackScreenProps<HomeStackParamList, "EditPatientProfile">;

const EditPatientProfile: React.FC<Props> = ({ navigation }) => {
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const [isModalVisible, setModalVisible] = React.useState(false);
  const [isScheduleModalVisible, setIsScheduleModalVisible] = React.useState(false);

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleModalScedule = () => {
    setIsScheduleModalVisible(!isScheduleModalVisible);
  };
  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
      <SafeAreaView style={styles.container}>
        {/* PROFILE_IMAGE */}
        <View style={styles.imageContainer}>
          <ImageBackground
            resizeMode="cover"
            source={user?.photo ? { uri: user?.photo } : EMPTY_AVATAR}
            style={user?.photo ? styles.imageContainer : styles.empty_avatar}
          ></ImageBackground>

          <ButtonwithIcon
            icon={<ArrowLeft fill="#fff" />}
            containerStyle={styles.icon}
            onPress={() => navigation.goBack()}
          />
          <ButtonwithIcon
            icon={<Edit3 height={28} width={28} />}
            containerStyle={{
              margin: 24,
              position: "absolute",
              top: 0,
              right: 0,
            }}
            onPress={() =>
              navigation.navigate("UpdateProfilePicture", {
                userId: user._id,
                isProfile: true,
              })
            }
          />
        </View>

        <View
          style={{
            marginTop: 18,
            paddingHorizontal: 20,
          }}
        >
          <View style={styles.iconContainer}>
            {/* NAME */}
            <Text style={styles.name}>{user?.name}</Text>
            <ButtonwithIcon
              icon={<Edit3 height={20} width={20} />}
              onPress={() => navigation.navigate("EditPatientInfo")}
            />
          </View>
          {/* ADDRES */}
          <TextButtonwithIcon
            leftIcon={<Location />}
            label={user?.address || "No address added"}
            labelStyle={styles.address}
            containerStyle={{ marginTop: 6 }}
            disabled
          />

          {/* DISCRIPTION */}
          {/* <Text style={styles.paragraph}>{user?.introduction}</Text> */}
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default EditPatientProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  imageContainer: {
    height: 280,
    width: "100%",
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  empty_avatar: {
    height: 150,
    width: 150,
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  address: {
    fontSize: 10,
    color: "#C7C7C7",
    marginLeft: 5,
    fontFamily: FONTS.Nunito_Regular,
  },
  info: {
    width: 90,
    height: 30,
    backgroundColor: "#EB4E1F20",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 30 / 2,
  },
  infoText: {
    fontSize: 10,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingText: {
    fontSize: 14,
    color: "#FCBD18",
    marginRight: 6,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingContainer: {
    width: 60,
    height: 30,
    backgroundColor: "#FCBD1830",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 60 / 2,
    marginLeft: 11,
  },
  subTitle: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 21,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 12,
    color: "#ABABAB",
    marginTop: 11,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  appointmentButton: {
    height: 50,
    borderRadius: 50 / 2,
    backgroundColor: COLORS.red,
    marginTop: 25,
  },
  appointmentModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scheduleModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  iconContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  card: {
    backgroundColor: "#FBFBFB",
    padding: 10,
    borderRadius: 10,
    marginTop: 10,
  },
  text: {
    fontSize: 11,
    color: "#263238",
    fontFamily: FONTS.Nunito_Regular,
  },
  title: {
    fontSize: 15,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  icon: {
    margin: 24,
    position: "absolute",
    top: 0,
    left: 0,
    width: 28,
    height: 26,
    borderRadius: 6,
    backgroundColor: "#00000080",
    alignItems: "center",
    justifyContent: "center",
    paddingRight: 2,
  },
});
