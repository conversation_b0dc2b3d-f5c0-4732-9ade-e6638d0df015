import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import { Profile } from "../../../assets/images";
import {
  AppButton,
  AppointmentSch<PERSON>le,
  ButtonwithIcon,
  ChooseAppointment,
  Rating,
  Review,
  TextButton,
} from "../../../components";
import { Calendar4, Star, Message1, VideoCall } from "../../../assets/svgicons";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import moment from "moment";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import RatingServices from "../../../Services/RatingServices";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import TextButtonWithIcon from "../../../components/TextButtonwithIcon";
import { updateBookingAction } from "../../../redux/slices/BookingsSlice";
import Animated, { FadeInDown, FadeOut, LinearTransition } from "react-native-reanimated";
import { EditBookingScreenParams } from "../../../models/IBooking";
import Modal from "react-native-modal";
import { IOfferService } from "../../../models/IOfferService";
import { useFormik } from "formik";
import BookingService from "../../../Services/BookingService";
import { showToast } from "../../../helper/toast";

type Props = NativeStackScreenProps<HomeStackParamList, "BookingDetails">;
interface IRating {
  ratingCount: number;
  ratingText: string;
}

const BookingDetails: React.FC<Props> = ({ navigation, route }) => {
  const userId = useAppSelector((state) => state.auth.userId);
  const { bookingId } = route.params;
  const dispatch = useAppDispatch();
  const { results: active_bookings } = useAppSelector((state) => state.bookings.active);
  const { results: completed_bookings } = useAppSelector((state) => state.bookings.completed);
  const booking = [...active_bookings, ...completed_bookings].find((item) => item._id === bookingId);

  console.log("patient.booking.details >>", JSON.stringify(booking, null, 2));

  const [rating, setRating] = useState<IRating>({
    ratingCount: 0,
    ratingText: "",
  });
  const [ratingLoading, setRatingLoading] = useState(false);
  const [isModalVisible, setModalVisible] = React.useState(false);
  const [isScheduleModalVisible, setIsScheduleModalVisible] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<IOfferService>();
  const [reSchduleBookingLoading, setReSchduleBookingLoading] = useState(false);

  const isWithinSessionTime = booking
    ? (() => {
        const bookingDateTime = moment(
          `${booking.bookingDateTime.date} ${booking.bookingDateTime.time}`,
          "YYYY-MM-DD HH:mm",
        );
        const sessionEndTime = bookingDateTime.clone().add(1, "hour");
        const now = moment();
        return now.isBetween(bookingDateTime, sessionEndTime, null, "[]");
      })()
    : false;

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleModalScedule = () => {
    setIsScheduleModalVisible(!isScheduleModalVisible);
  };

  useEffect(() => {
    navigation.setOptions({
      headerTitle: booking?.status === "active" ? "Bookings" : "Past Bookings",
    });
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <ButtonwithIcon icon={<Calendar4 />} onPress={() => navigation.navigate("PatientAgendaView")} />
      ),
    });
  }, [navigation]);

  const formik = useFormik({
    initialValues: {
      service: booking.service,
      bookingDateTime: booking.bookingDateTime,
    },
    enableReinitialize: true,
    onSubmit: async (formValues) => {
      setReSchduleBookingLoading(true);
      const bookingPayload = {
        doctor: booking.doctor._id,
        patient: userId,
        bookingDateTime: formValues.bookingDateTime,
        service: formValues.service,
      };
      try {
        await BookingService.reschduleBookingApi(bookingId, bookingPayload).then((response) => {
          dispatch(updateBookingAction(response.data));
          showToast("Reschedule booking successful", "Success", "success");
          toggleModalScedule();
          setReSchduleBookingLoading(false);
        });
      } catch (error) {
        console.log("error while reschdule booking", error.response);
        toggleModalScedule();
        showToast("Something went wrong");
        setReSchduleBookingLoading(false);
      }
    },
  });

  const addReviewOnBooking = async (bookingId: string) => {
    setRatingLoading(true);
    const payload = {
      rating: rating.ratingCount,
      review: rating.ratingText,
      reviewBy: userId,
      bookingId: booking._id,
    };
    await RatingServices.AddRating(booking.doctor._id, payload)
      .then((respose) => {
        dispatch(
          updateBookingAction({
            _id: booking._id,
            review: { rating: payload.rating, review: payload.review },
          }),
        );
      })
      .catch((error) => {
        console.log("error---------", error);
      })
      .finally(() => {
        setRatingLoading(false);
      });
  };
  const rebookAppointment = () => {
    const currentDay = moment(booking.bookingDateTime.date).day();
    const upcoming_booking_date = moment()
      .day(currentDay + 7)
      .format("YYYY-MM-DD");

    console.log("currentDay", currentDay);
    console.log("booking_date_in_upcoming_week", upcoming_booking_date);

    const bookingData: EditBookingScreenParams = {
      bookingDateTime: {
        date: upcoming_booking_date,
        time: booking.bookingDateTime.time,
        day: booking.bookingDateTime.day,
      },
      doctor: booking.doctor,
      selectedAppointment: booking.selectedAppointment,
      service: booking.service,
    };
    navigation.navigate("EditBookingDetails", { bookingData, shouldGoBack: true });
  };

  // const isBookingActive = () => {
  //   const now = moment();
  //   const bookingStart = moment(bookingDateTime);
  //   const sessionTime = parseInt(booking.service.sessionTime, 10);
  //   const bookingEnd = moment(bookingDateTime).add(sessionTime, 'minutes');
  //   return now.isBetween(moment(bookingStart), moment(bookingEnd));
  // };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView showsVerticalScrollIndicator={false} extraScrollHeight={12}>
        {/* APPOINTED_CARD */}
        <View style={styles.card}>
          <Text style={styles.text}>Appointment:</Text>
          <Text style={[styles.title, { marginTop: 14 }]}>{booking?.service?.name}</Text>
          <Text style={[styles.text, { marginTop: 8 }]}>1 hour @ ${booking?.service?.price}</Text>
        </View>
        {/* DOCTOR */}
        <View style={[styles.card, { marginTop: 16 }]}>
          <Text style={[styles.text, { marginBottom: 14 }]}>Doctor:</Text>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Image source={{ uri: booking.doctor.photo }} style={{ width: 32, height: 32, borderRadius: 32 / 2 }} />
            <Text style={[styles.title, { marginLeft: 7, flex: 1 }]}>{booking?.doctor?.name}</Text>
            {/* MESSAGE_BUTTON */}
            <ButtonwithIcon
              icon={<Message1 />}
              onPress={() => {
                navigation.navigate("ChatDetails", {
                  otherUser: booking.doctor,
                });
              }}
            />
          </View>
        </View>
        {/* TIME */}
        <View style={[styles.card, { marginTop: 16 }]}>
          <Text style={styles.text}>Date & Time:</Text>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Text style={styles.title}>{moment(booking?.bookingDateTime?.date).format("MMM DD, dddd")}</Text>
            <View style={styles.dot} />
            <Text style={styles.title}>{booking?.bookingDateTime?.time}</Text>
          </View>
        </View>
        {/* MODE */}
        {/* <View style={[styles.card, {marginTop: 16}]}>
          <Text style={styles.text}>Mode:</Text>
          <View style={{flexDirection: 'row', marginTop: 15}}>
            <ButtonwithIcon icon={<Call1 />} />

            <ButtonwithIcon
              icon={<VideoCall />}
              containerStyle={{marginLeft: 12}}
              disabled
              // onPress={() => {
              //   // if (isBookingActive()) {
              //   navigation.navigate('Session', {sessionId: bookingId});
              //   // } else {
              //   //   Alert.alert(
              //   //     'Session Inactive',
              //   //     'The session time has either passed or not started yet.',
              //   //   );
              //   // }
              // }}
            />
          </View>
        </View> */}
        {booking?.status === "completed" && (
          <View style={[styles.card, { marginTop: 16 }]}>
            {booking?.review?.rating ? (
              <View>
                <Text style={[styles.text, { marginVertical: 10 }]}>Rate:</Text>
                <TextButtonWithIcon
                  leftIcon={<Star />}
                  containerStyle={styles.ratingText}
                  label={booking?.review?.rating.toString()}
                  labelStyle={styles.ratingText}
                  disabled
                />
                <Text style={{ color: "#4C4C4C", marginTop: 10, fontSize: 14 }}>{booking?.review?.review}</Text>
              </View>
            ) : (
              <View>
                <Rating
                  setRating={setRating}
                  rating={rating}
                  onPressRatingButton={() => addReviewOnBooking(booking._id)}
                  ratingLoading={ratingLoading}
                  title="Rate:"
                />
              </View>
            )}
          </View>
        )}
        {booking?.status === "completed" ? (
          <>
            <Text style={[styles.text, { color: "#263238", marginTop: 24 }]}>Want to Reappoint?</Text>
            <TouchableOpacity onPress={rebookAppointment}>
              <Text style={[styles.scheduleText]}>Reappoint</Text>
            </TouchableOpacity>
          </>
        ) : (
          <>
            <Text style={[styles.text, { color: "#263238", marginTop: 24 }]}>Want to Reschedule?</Text>
            <TouchableOpacity onPress={toggleModalScedule}>
              <Text style={[styles.scheduleText]}>Reschedule</Text>
            </TouchableOpacity>
          </>
        )}

        {booking?.status === "active" && (
          <View style={styles.footer}>
            <TextButton
              label={"Start Session"}
              containerStyle={styles.startSessionButton}
              onPress={() => {
                navigation.navigate("Session", {
                  sessionId: bookingId,
                  doctorId: booking.doctor._id,
                });
              }}
            />
            {/* <View style={styles.cancelButtonContainer}>
              <Text style={styles.text}>Something happened?</Text>
              <TextButton label={'Cancel?'} labelStyle={{color: COLORS.red}} />
            </View> */}
          </View>
        )}
      </KeyboardAwareScrollView>

      {/* CHOOSE SERVICE MODAL */}
      <Modal
        isVisible={isModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModal()}
        swipeDirection={"down"}
        onSwipeComplete={() => setModalVisible(false)}
        style={styles.appointmentModalContainer}
      >
        <ChooseAppointment
          currentService={booking.service}
          services={booking.doctor.services}
          onContinuePress={(v) => {
            formik.setFieldValue("service", v);
            toggleModal();
            setTimeout(() => {
              toggleModalScedule();
            }, 500);
          }}
          setSelectedAppointment={setSelectedAppointment}
        />
      </Modal>

      {/* CHOOSE DATE-TIME SLOT MODAL */}
      <Modal
        isVisible={isScheduleModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModalScedule()}
        style={styles.scheduleModalContainer}
        swipeDirection={"down"}
        animationOutTiming={500}
        onSwipeComplete={() => setIsScheduleModalVisible(false)}
      >
        <View>
          <AppointmentSchdule
            schedule={booking.doctor.schedule}
            doctorId={booking.doctor._id}
            onPress={(v) => {
              formik.setFieldValue("bookingDateTime", v);
              formik.handleSubmit();
            }}
            selectedAppointment={selectedAppointment}
            reSchduleBookingLoading={reSchduleBookingLoading}
          />
        </View>
      </Modal>
    </View>
  );
};

export default BookingDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
  },
  card: {
    backgroundColor: "#FBFBFB",
    padding: 10,
    borderRadius: 10,
  },
  text: {
    fontSize: 11,
    color: "#263238",
    fontFamily: FONTS.Nunito_Regular,
  },
  title: {
    fontSize: 15,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  dot: {
    width: 7,
    height: 7,
    borderRadius: 7 / 2,
    backgroundColor: "#D9D9D9",
    marginHorizontal: 13,
  },
  scheduleText: {
    fontSize: 14,
    color: "#EB4E1F",
    marginTop: 10,
    fontFamily: FONTS.Nunito_Bold,
  },
  startSessionButton: {
    height: 48,
    backgroundColor: COLORS.red,
    marginTop: 28,
    borderRadius: 8,
  },

  footer: {
    flex: 1,
    justifyContent: "space-between",
    marginTop: 30,
  },
  cancelButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 16,
  },
  ratingText: {
    fontSize: 17,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
    paddingLeft: 6,
    // marginTop: 2,
  },
  starImage: {
    alignSelf: "flex-start",
    marginTop: 20,
  },
  appointmentModalContainer: {
    margin: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: "flex-end",
  },
  scheduleModalContainer: {
    margin: 0,
    justifyContent: "flex-end",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});
