import { Image, SafeAreaView, StatusBar, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { Bell, OW1 } from "../../../assets/svgicons";
import { <PERSON>B<PERSON>, MaleFront, person, FEMALE_BACK, FEMALE_FRONT } from "../../../assets/images";
import { ButtonwithIcon, Point, TextButton } from "../../../components";
import { FrontBodyParts, BackBodyParts } from "../../../constants";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { BottomStackParamList } from "../../../navigation/BottomTab";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { fetchActiveBookingsThunk, fetchCompletedBookingsThunk } from "../../../redux/slices/BookingsSlice";
type Props = NativeStackScreenProps<BottomStackParamList, "Home">;

const Home: React.FC<Props> = ({ navigation }) => {
  const [selectedImage, setSelectedImage] = useState("front");

  const [frontParts, setFrontParts] = useState(FrontBodyParts);
  const [backParts, setbackParts] = useState(BackBodyParts);
  const dispatch = useAppDispatch();

  const { user, userId } = useAppSelector((state) => state.auth);

  const handleOpenedPointFront = (key) => {
    var tempObject = [...FrontBodyParts];
    tempObject.map((item) => {
      if (item.notSelected.key === key) {
        if (item.isOpened) {
          item.isOpened = false;
        } else {
          item.isOpened = true;
        }
      } else {
        item.isOpened = false;
      }
    });
    setFrontParts(tempObject);
  };

  const handleOpenedPointBack = (key) => {
    var tempObject = [...BackBodyParts];
    tempObject.map((item) => {
      if (item.notSelected.key === key) {
        if (item.isOpened) {
          item.isOpened = false;
        } else {
          item.isOpened = true;
        }
      } else {
        item.isOpened = false;
      }
    });
    setbackParts(tempObject);
  };

  const humanImage = () => {
    if (user.gender === "male") {
      if (selectedImage === "front") {
        return MaleFront;
      } else if (selectedImage === "back") {
        return MaleBack;
      }
    } else if (user.gender === "female") {
      if (selectedImage === "front") {
        return FEMALE_FRONT;
      } else if (selectedImage === "back") {
        return FEMALE_BACK;
      }
    }
  };

  useEffect(() => {
    if (userId) {
      dispatch(fetchActiveBookingsThunk({ userId, userType: "patient" }));
      dispatch(fetchCompletedBookingsThunk({ userId, userType: "patient" }));
    }
  }, [userId]);

  return (
    <SafeAreaView style={styles.container}>
      {/* HEADER */}
      <StatusBar backgroundColor={COLORS.white} />

      <View style={styles.headerContainer}>
        {/* NOTIFICATION_ICON */}
        <ButtonwithIcon icon={<Bell />} onPress={() => navigation.navigate("Notification")} />
        {/* LOGO */}
        <OW1 width={100} height={35} />
        {/* PROFILE_IMAGE_Button */}
        <ButtonwithIcon
          icon={
            <Image
              source={user.photo ? { uri: user.photo } : person}
              style={{ width: 30, height: 30, borderRadius: 30 }}
            />
          }
          onPress={() => navigation.navigate("Profile")}
        />
      </View>
      {/* DIVIDER */}
      <View style={styles.divider} />

      {/* FORONT_&_BAK */}
      <View style={styles.innerContainer}>
        {/* TITLE_&_SUB_TITLE */}
        <View>
          <Text style={styles.title}>Diagnostic Tool</Text>
          <Text style={styles.subTitle}>Select Your Pain Area</Text>
        </View>
        {/* FRONT_&_BACK_BUTTON_CONTAINER */}
        <View style={{ flexDirection: "row" }}>
          {/* FRONT_BUTTON */}
          <TextButton
            label={"Front"}
            containerStyle={{
              ...styles.frontButton,
              backgroundColor: selectedImage === "front" ? COLORS.red : "#F7F7F7",
            }}
            labelStyle={{
              fontSize: 12,
              color: selectedImage === "front" ? COLORS.white : COLORS.black,
            }}
            onPress={() => {
              setSelectedImage("front");
              setbackParts(backParts.map((item) => ({ ...item, isOpened: false })));
            }}
          />
          {/* BACK_BUTTON */}
          <TextButton
            label={"Back"}
            containerStyle={{
              ...styles.frontButton,
              backgroundColor: selectedImage === "back" ? COLORS.red : "#F7F7F7",
            }}
            labelStyle={{
              fontSize: 12,
              color: selectedImage === "back" ? COLORS.white : COLORS.black,
            }}
            onPress={() => {
              setSelectedImage("back");
              setFrontParts(frontParts.map((item) => ({ ...item, isOpened: false })));
            }}
          />
        </View>
      </View>
      {/* FRONT_&_BACK_IMAGE */}

      <View style={{ height: 470 }}>
        {selectedImage == "front" ? (
          <View style={{ zIndex: 30 }}>
            {frontParts.map((item, index) => {
              return (
                <Point handleOpenedPoint={handleOpenedPointFront} key={`items-${index}`} item={item} front={true} />
              );
            })}
          </View>
        ) : (
          <View style={{ zIndex: 50 }}>
            {backParts.map((item, index) => {
              return (
                <Point handleOpenedPoint={handleOpenedPointBack} key={`items-${index}`} item={item} front={false} />
              );
            })}
          </View>
        )}

        <Image resizeMode="contain" source={humanImage()} style={styles.image} />
      </View>
      <View style={{ flex: 1, justifyContent: "flex-end" }}>
        {/* FIND_A_DOCTOR_BUTTON */}
        <TextButton
          label={"Find a Doctor"}
          containerStyle={styles.button}
          onPress={() =>
            navigation.navigate("ChooseDoctor", {
              pain:
                selectedImage === "front"
                  ? frontParts?.find((item) => item?.isOpened)?.selected?.label
                  : backParts?.find((item) => item?.isOpened)?.selected?.label,
            })
          }
        />
      </View>
    </SafeAreaView>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: 20,
  },
  divider: {
    backgroundColor: "#F2F2F2",
    height: 1,
    marginTop: 15,
  },
  innerContainer: {
    flexDirection: "row",
    marginTop: 12,
    paddingHorizontal: 20,
    justifyContent: "space-between",
  },
  title: {
    fontSize: 15,
    color: "#263238",
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    fontSize: 12,
    color: "#525252",
    fontFamily: FONTS.Nunito_Regular,
  },
  frontButton: {
    width: 56,
    height: 30,
    backgroundColor: COLORS.red,
    borderRadius: 4,
  },
  image: {
    width: "100%",
    height: 470,
    marginTop: 35,
    alignSelf: "center",
  },
  button: {
    backgroundColor: COLORS.red,
    height: 49,
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 9,
  },
  subTitle: {
    fontFamily: FONTS.Nunito_Medium,
    fontSize: 12,
  },
});
