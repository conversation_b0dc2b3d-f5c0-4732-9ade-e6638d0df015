import {
  <PERSON><PERSON>View,
  StatusBar,
  StyleSheet,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import React, {FC} from 'react';
import {COLORS, FONTS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import RenderHTML from 'react-native-render-html';

type Props = NativeStackScreenProps<HomeStackParamList, 'TermsAndConditions'>;

const TermsAndConditions: FC<Props> = () => {
  const {width} = useWindowDimensions();

  const termsAndConditionsHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Terms and Conditions - OW</title>
        <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
        * {
        color:red;
        }
            body {
                font-family: 'Nunito', sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background-color: #f9f9f9;
                color: #000;
            }
            h1, h2, h3 {
              color: #000;
            }
            h1 {
                font-size: 2em;
                margin-bottom: 20px;
            }
            h2 {
                font-size: 1.5em;
                margin-top: 20px;
                margin-bottom: 10px;
            }
            p {
                margin-bottom: 15px;
            }
            ul {
                margin-left: 20px;
            }
            ul li {
                margin-bottom: 10px;
            }
            a {
                color: #007bff;
                text-decoration: none;
            }
            a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <h1>Terms and Conditions</h1>
        <p>Welcome to OW! By using our app, you agree to comply with and be bound by the following terms and conditions. Please read these terms carefully before signing up as a patient or doctor, or using any of our services.</p>
        <h2>1. Acceptance of Terms</h2>
        <p>By creating an account or accessing our services, you agree to these Terms and Conditions. If you do not agree, you must not use the app.</p>
        <h2>2. User Roles</h2>
        <p>You can sign up on OW as either:</p>
        <ul>
            <li><strong>Patient:</strong> To book and manage appointments with doctors.</li>
            <li><strong>Doctor:</strong> To offer consultation sessions and manage patient appointments.</li>
        </ul>
        <h2>3. User Responsibilities</h2>
        <p>All users must:</p>
        <ul>
            <li>Provide accurate and truthful information during registration.</li>
            <li>Keep their account credentials secure and confidential.</li>
            <li>Not engage in any illegal or prohibited activities through the app.</li>
        </ul>
        <h2>4. Booking and Cancellation</h2>
        <p>Patients can book appointments with doctors through the app. Cancellation policies are as follows:</p>
        <ul>
            <li>Appointments can be canceled up to 24 hours before the scheduled time without any charges.</li>
            <li>Late cancellations or no-shows may result in a fee as determined by the doctor.</li>
        </ul>
        <h2>5. Payment Terms</h2>
        <p>All payments for doctor appointments must be made through the app. We use secure payment gateways to process transactions. OW does not store your payment details.</p>
        <h2>6. Medical Disclaimer</h2>
        <p>OW is a platform for connecting patients and doctors. We do not provide medical advice, diagnosis, or treatment. Any medical consultation provided by doctors is their sole responsibility.</p>
        <h2>7. Limitation of Liability</h2>
        <p>OW is not responsible for any issues arising from doctor-patient interactions, including but not limited to:</p>
        <ul>
            <li>Misdiagnosis or incorrect treatment.</li>
            <li>Missed appointments.</li>
            <li>Disputes between users.</li>
        </ul>
        <h2>8. Termination of Account</h2>
        <p>We reserve the right to suspend or terminate your account if you violate these terms or engage in any activity that harms our platform or other users.</p>
        <h2>9. Changes to Terms</h2>
        <p>We may update these Terms and Conditions from time to time. You will be notified of any significant changes. Continued use of the app constitutes your acceptance of the revised terms.</p>
        <h2>10. Contact Us</h2>
        <p>If you have any questions or concerns about these Terms and Conditions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        <p>By using OW, you acknowledge that you have read, understood, and agreed to these Terms and Conditions.</p>
    </body>
    </html>
  `;

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      <ScrollView
        contentContainerStyle={{paddingBottom: 32}}
        showsVerticalScrollIndicator={false}>
        <RenderHTML
          contentWidth={width}
          source={{html: termsAndConditionsHtml}}
          baseStyle={{color: 'black'}} // Ensures all text remains black
        />
      </ScrollView>
    </View>
  );
};

export default TermsAndConditions;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 10,
    paddingHorizontal: 20,
  },
});
