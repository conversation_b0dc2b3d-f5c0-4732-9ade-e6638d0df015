import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {Agenda} from 'react-native-calendars';
import {useAppSelector} from '../../../redux/config/Store';
import {COLORS, FONTS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import BookingService from '../../../Services/BookingService';
import {IBooking} from '../../../models/IBooking';

// Define types for agenda items and marked dates
type AgendaItem = {
  name: string;
  doctor: string;
  time: string;
  id: string;
};

type AgendaItems = {
  [key: string]: AgendaItem[];
};

type MarkedDates = {
  [key: string]: {
    marked: boolean;
    dotColor?: string;
    selected?: boolean;
    selectedColor?: string;
  };
};

// Define props for the component based on navigation stack
type Props = NativeStackScreenProps<HomeStackParamList, 'PatientAgendaView'>;

const PatientAgendaView: React.FC<Props> = ({navigation}) => {
  const userId = useAppSelector(state => state.auth.userId);
  // const {results} = useAppSelector(state => state.bookings);
  const [results, setResults] = useState<IBooking[]>([]);
  const [items, setItems] = useState<AgendaItems>({});
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split('T')[0], // Default to today's date
  );
  const [markedDates, setMarkedDates] = useState<MarkedDates>({});
  const [loading, setLoading] = useState(true);

  const getBooking = async () => {
    setLoading(true);
    try {
      const response = await BookingService.GetBookingsByDateFilter({
        patient: userId,
        date: selectedDate,
      });
      setLoading(false);

      setResults(response.data?.results);
    } catch (error) {
      console.log('FETCH BOOKINGS THUNK >> ', error.response);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getBooking();
  }, [selectedDate]);

  useEffect(() => {
    const filteredItems: AgendaItems = {};
    const tempMarkedDates: MarkedDates = {};

    // Loop through booking results
    results.forEach(booking => {
      const {bookingDateTime, doctor, service, _id} = booking;
      const bookingDate = bookingDateTime.date; // Extract "YYYY-MM-DD"

      // Mark booked dates with a dot (🔵)
      if (!tempMarkedDates[bookingDate]) {
        tempMarkedDates[bookingDate] = {marked: true, dotColor: 'blue'};
      }

      // Only show bookings for the selected date
      if (bookingDate !== selectedDate) return;

      // Convert session time to minutes if needed
      let sessionTime = Number(service?.sessionTime || 0);
      if (sessionTime === 1) {
        sessionTime = 60; // Convert 1 hour to 60 minutes
      }

      // Calculate end time for session
      const [startHour, startMinute] = bookingDateTime.time
        .split(':')
        .map(Number);
      const endTime = new Date();
      endTime.setHours(startHour);
      endTime.setMinutes(startMinute + sessionTime);
      const formattedEndTime = endTime.toTimeString().slice(0, 5); // Format HH:mm

      // Add booking to the agenda items
      if (!filteredItems[bookingDate]) {
        filteredItems[bookingDate] = [];
      }

      filteredItems[bookingDate].push({
        name: service?.name || 'Unknown Service',
        doctor: doctor?.name || 'Unknown Doctor',
        time: `${bookingDateTime.time} - ${formattedEndTime}`,
        id: _id,
      });
    });

    // Highlight selected date separately (🟠)
    tempMarkedDates[selectedDate] = {
      ...tempMarkedDates[selectedDate],
      selected: true,
      selectedColor: 'orange',
    };

    // Update state
    setItems(filteredItems);
    setMarkedDates(tempMarkedDates);
  }, [results, selectedDate]);

  return (
    <View style={styles.container}>
      <Agenda
        items={items}
        selected={selectedDate}
        markedDates={markedDates} // ✅ Marks booked dates and selected date
        onDayPress={day => setSelectedDate(day.dateString)} // ✅ Updates selected date on tap
        renderItem={(item: AgendaItem) => {
          return (
            <TouchableOpacity
              style={styles.item}
              onPress={() =>
                navigation.navigate('BookingDetails', {bookingId: item.id})
              }>
              <Text style={styles.name}>Session with {item.doctor}</Text>
              <Text style={styles.speciality}>Therapist</Text>
              <Text style={styles.time}>{item.time}</Text>
            </TouchableOpacity>
          );
        }}
        renderEmptyData={() => <Text style={styles.empty}>No Bookings</Text>} // Shows when no bookings exist
      />
    </View>
  );
};

// Styles for the component
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  item: {
    flex: 1,
    borderRadius: 12,
    padding: 12,
    marginRight: 20,
    marginTop: 25,
    backgroundColor: '#EB4E1F25',
  },
  name: {
    fontSize: 14,
    fontFamily: FONTS.Nunito_Bold,
    color: COLORS.black,
  },
  speciality: {
    fontSize: 11,
    color: '#818E9C',
    fontFamily: FONTS.Nunito_Light,
    paddingTop: 2,
  },
  time: {
    fontSize: 11,
    color: '#818E9C',
    fontFamily: FONTS.Nunito_Regular,
    paddingTop: 18,
  },
  empty: {
    textAlign: 'center',
    marginTop: 20,
    color: 'gray',
    fontFamily: FONTS.Nunito_Medium,
  },
});

export default PatientAgendaView;
