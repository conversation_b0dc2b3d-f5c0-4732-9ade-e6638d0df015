import React from "react";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { View, StyleSheet, ScrollView } from "react-native";

const RecommendedDoctorSkeleton = () => {
  return (
    <SkeletonPlaceholder>
      <ScrollView horizontal style={{ flexDirection: "row", gap: 20, marginLeft: 20 }}>
        <View style={{ paddingBottom: 30, marginBottom: 13 }}>
          <View style={styles.card}>
            <View style={styles.badge} />

            <View style={styles.circle} />

            <View style={styles.content}>
              <View style={{ flex: 1 }}>
                <View style={styles.namePlaceholder} />
                <View style={styles.subtextPlaceholder} />
              </View>

              <View style={styles.Btn} />
            </View>
          </View>
        </View>
        <View style={{ paddingBottom: 30, marginBottom: 13 }}>
          <View style={styles.card}>
            <View style={styles.badge} />

            <View style={styles.circle} />

            <View style={styles.content}>
              <View style={{ flex: 1 }}>
                <View style={styles.namePlaceholder} />
                <View style={styles.subtextPlaceholder} />
              </View>

              <View style={styles.Btn} />
            </View>
          </View>
        </View>
      </ScrollView>
    </SkeletonPlaceholder>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    justifyContent: "space-between",
    width: 250,
    height: 300,
    borderWidth: 1,
    borderColor: "#000",
  },
  badge: {
    width: 120,
    height: 28,
    borderRadius: 4,
  },
  circle: {
    width: 160,
    height: 160,
    borderRadius: 160,
    alignSelf: "center",
  },
  image: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginBottom: 16,
  },
  content: {
    // width: '100%',
    marginHorizontal: 24,
    padding: 12,
    borderWidth: 1,
    borderColor: "#000",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  namePlaceholder: {
    width: "50%",
    height: 10,
    borderRadius: 4,
    marginBottom: 8,
  },
  subtextPlaceholder: {
    width: "25%",
    height: 6,
    borderRadius: 4,
  },
  Btn: {
    width: "30%",
    height: 24,
    borderRadius: 4,
  },
  ratingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  ratingPlaceholder: {
    width: 60,
    height: 20,
    borderRadius: 4,
  },
  buttonPlaceholder: {
    width: 60,
    height: 30,
    borderRadius: 4,
  },
});

export default RecommendedDoctorSkeleton;
