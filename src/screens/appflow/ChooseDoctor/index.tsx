import { FlatList, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import { DoctorsCard, RecommendedDoctorsCard } from "../../../components";
import { ScrollView } from "react-native";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { IUser } from "../../../models";
import { AxiosError } from "axios";
import DoctorCardSkeleton from "./DoctorCardSkeleton";
import RecommendedDoctorSkeleton from "./RecommendedDoctorSkeleton";
import UserService from "../../../Services/UserService";
type Props = NativeStackScreenProps<HomeStackParamList, "ChooseDoctor">;

const ChooseDoctor: React.FC<Props> = ({ navigation, route }) => {
  const { pain } = route.params;

  const [doctors, setDoctors] = useState<IUser[]>([]);
  const [recommendedDoctors, setRecommendedDoctors] = useState<IUser[]>([]);
  const [isLoading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    async function getDoctors() {
      setLoading(true);
      const params = {
        painType: pain,
        isVerified: true,
        isOnboarded: true,
        userType: "doctor",
      };

      await UserService.GetUsers(params)
        .then((response) => {
          // console.log("doctors >> ", response.data);

          setDoctors(response.data.results);
        })
        .catch((error: AxiosError) => {
          console.log("GET DOCTORS >> ", error.response.data);
        });
      setLoading(false);
    }

    async function getRecommendedDoctors() {
      setLoading(true);
      const params = {
        painType: pain,
        isVerified: true,
        isOnboarded: true,
        userType: "doctor",
        rating: 5,
      };
      await UserService.GetUsers(params)
        .then((response) => {
          // console.log("recommended >> ", response.data);

          setRecommendedDoctors(response.data.results);
        })
        .catch((error: AxiosError) => {
          console.log("getRecommendedDoctors >> ", error.response.data);
        });
      setLoading(false);
    }

    getDoctors();
    getRecommendedDoctors();
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* TITLE */}
        <Text style={styles.title}>Best Fit</Text>
        {/*  */}

        {isLoading ? <DoctorCardSkeleton /> : null}

        {/* DOCTORS CARD LIST */}
        {!isLoading && doctors?.length
          ? doctors?.map((item, index) => {
              return (
                <RecommendedDoctorsCard
                  key={`doctors-${index}`}
                  item={item}
                  containerStyle={{ marginBottom: 13, marginHorizontal: 20 }}
                  onPress={() => {
                    navigation.navigate("DoctorDetails", { doctor: item });
                  }}
                />
              );
            })
          : null}

        {!isLoading && !doctors?.length ? <Text style={styles.noDataText}>Currently no data for doctors..</Text> : null}

        <Text style={[styles.title, { paddingTop: 8, paddingBottom: 12 }]}>Recommended</Text>

        {isLoading ? <RecommendedDoctorSkeleton /> : null}

        {/* RECOMENDED DOCTORS */}
        {!isLoading && recommendedDoctors?.length ? (
          <View style={{ paddingBottom: 30 }}>
            <FlatList
              data={recommendedDoctors}
              contentContainerStyle={{ paddingHorizontal: 20, gap: 20 }}
              horizontal
              showsHorizontalScrollIndicator={false}
              renderItem={({ item }) => {
                return (
                  <DoctorsCard
                    item={item}
                    // containerStyle={{marginRight: 20}}
                    onPress={() => navigation.navigate("DoctorDetails", { doctor: item })}
                  />
                );
              }}
            />
          </View>
        ) : null}

        {!isLoading && !recommendedDoctors?.length ? (
          <Text style={styles.noDataText}>Currently no recommendation for doctors..</Text>
        ) : null}
      </ScrollView>
    </View>
  );
};

export default ChooseDoctor;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 10,
  },
  headerContainer: {
    backgroundColor: COLORS.white,
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.red,
    marginTop: 25,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    marginHorizontal: 20,
  },
  headerText: {
    fontSize: 16,
    color: COLORS.red,
    marginLeft: 8,
    flex: 1,
    fontFamily: FONTS.Nunito_Bold,
  },
  title: {
    fontSize: 20,
    color: "#263238",
    paddingTop: 40,
    fontFamily: FONTS.Nunito_Bold,
    paddingBottom: 12,
    paddingLeft: 20,
  },
  diagnosticToolCar: {
    backgroundColor: "#263238",
    marginTop: 50,
    paddingVertical: 14,
    paddingHorizontal: 22,
  },
  paragraph: {
    fontSize: 13,
    color: COLORS.white,
    marginTop: 11,
    fontFamily: FONTS.Nunito_Bold,
  },
  exploreButton: {
    height: 32,
    backgroundColor: COLORS.red,
    width: 80,
    borderRadius: 7,
  },
  learnmoreButton: {
    height: 32,
    backgroundColor: null,
    width: 80,
    borderRadius: 7,
    marginLeft: 15,
  },
  noDataText: { textAlign: "center", color: "#000666" },
});
