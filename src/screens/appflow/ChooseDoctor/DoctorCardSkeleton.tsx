import React from "react";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";
import { View, StyleSheet } from "react-native";

const DoctorCardSkeleton = () => {
  return (
    <>
      <SkeletonPlaceholder>
        <>
          {Array.from({ length: 4 }).map((_, index) => (
            <View style={styles.card} key={index}>
              {/* Image placeholder */}
              <View style={styles.image} />

              {/* Text placeholders */}
              <View style={styles.content}>
                {/* Name and Title */}
                <View style={styles.nameRow}>
                  <View>
                    <View style={styles.namePlaceholder} />
                    <View style={styles.expPlaceholder} />
                  </View>
                  <View style={styles.ratingPlaceholder} />
                </View>

                {/* <View style={styles.subtextPlaceholder} /> */}

                {/* Book Button */}
                <View style={[styles.nameRow]}>
                  <View style={styles.namePlaceholder} />
                  <View style={styles.ratingPlaceholder} />
                </View>
              </View>
            </View>
          ))}
        </>
      </SkeletonPlaceholder>
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "black",
    marginBottom: 13,
    marginHorizontal: 20,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 10,
  },
  content: {
    flex: 1,
    paddingLeft: 10,
  },
  nameRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    // alignItems: 'center',
    marginBottom: 4,
  },
  namePlaceholder: {
    width: 100,
    height: 16,
    borderRadius: 4,
  },
  ratingPlaceholder: {
    width: 40,
    height: 16,
    borderRadius: 4,
  },
  subtextPlaceholder: {
    width: 80,
    height: 16,
    borderRadius: 4,
    marginBottom: 4,
  },
  expPlaceholder: {
    width: 50,
    height: 12,
    borderRadius: 4,
    marginBottom: 8,
    marginTop: 6,
  },
  buttonPlaceholder: {
    width: 80,
    height: 30,
    borderRadius: 4,
    alignSelf: "flex-start",
  },
});

export default DoctorCardSkeleton;
