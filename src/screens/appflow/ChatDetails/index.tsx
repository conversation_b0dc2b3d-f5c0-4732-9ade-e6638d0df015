import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  Image,
  ActivityIndicator,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import React, {useEffect} from 'react';
import {
  Bubble,
  GiftedChat,
  InputToolbar,
  Send,
  Time,
} from 'react-native-gifted-chat';
import {COLORS, FONTS} from '../../../themes';
import {ArrowLeft, SendFill} from '../../../assets/svgicons';
import {ButtonwithIcon} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import {useAppSelector} from '../../../redux/config/Store';
import {IChat} from '../../../models/IChat';

import useConvetsation from '../../../Hooks/useConvetsation';
import FastImage from 'react-native-fast-image';

type Props = NativeStackScreenProps<HomeStackParamList, 'ChatDetails'>;

const ChatDetails: React.FC<Props> = ({navigation, route}) => {
  const {otherUser} = route.params;

  const {userId} = useAppSelector(state => state.auth);
  const {onSend, getAllMessages, messages, messagesLoading} = useConvetsation();

  useEffect(() => {
    otherUser._id && getAllMessages(otherUser._id);
  }, []);

  const onSendMessage = async (messages: IChat[] = []) => {
    onSend(messages, otherUser._id);
  };

  return (
    <SafeAreaView style={styles.container}>
      {messagesLoading ? (
        <ActivityIndicator
          size={'large'}
          style={{marginTop: Dimensions.get('screen').height / 3}}
        />
      ) : (
        <>
          {/* HEADER */}
          <View style={styles.header}>
            {/* ARROW_LEFT */}
            <ButtonwithIcon
              icon={<ArrowLeft fill="#000" />}
              containerStyle={{marginRight: 15}}
              onPress={() => navigation.goBack()}
            />
            {/* PROFILE_IMAGE */}
            {otherUser?.photo ? (
              <View
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#E9E9E9',
                }}>
                <FastImage
                  source={{uri: otherUser?.photo}}
                  style={{width: 40, height: 40, borderRadius: 20}}
                />
              </View>
            ) : null}
            {!otherUser?.photo ? (
              <View
                style={{
                  height: 40,
                  width: 40,
                  borderRadius: 20,
                  backgroundColor: '#f0f0f0',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{color: 'black'}}>
                  {otherUser?.name?.split('')[0].toLocaleUpperCase()}
                </Text>
              </View>
            ) : null}
            {/* NAME */}
            <Text style={styles.name}>{otherUser?.name}</Text>
          </View>

          <GiftedChat
            messages={messages}
            onSend={messages => onSendMessage(messages)}
            user={{
              _id: userId,
            }}
            showUserAvatar={false}
            keyboardShouldPersistTaps={'never'}
            showAvatarForEveryMessage={false}
            alwaysShowSend
            listViewProps={{showsVerticalScrollIndicator: false}}
            scrollToBottom
            inverted={messages?.length == 0 ? false : true}
            messagesContainerStyle={
              messages?.length == 0 && {transform: [{scaleY: -1}]}
            }
            renderChatEmpty={() => (
              <Text style={styles.heading}>No messages!</Text>
            )}
            placeholder="Write  a reply..."
            textInputProps={{
              color: '#000',
              // multiline: false,
              paddingRight: 56,
            }}
            renderBubble={props => {
              return (
                <Bubble
                  {...props}
                  wrapperStyle={{
                    right: {
                      backgroundColor: COLORS.red,
                      borderRadius: 0,
                      borderTopLeftRadius: 12,
                      borderTopRightRadius: 12,
                      borderBottomLeftRadius: 12,
                      borderBottomRightRadius: 0,
                      padding: 5,
                      marginBottom: 15,
                      marginRight: 20,
                    },
                    left: {
                      backgroundColor: '#E9E9E9',
                      borderRadius: 0,
                      borderTopLeftRadius: 0,
                      borderTopRightRadius: 12,
                      borderBottomLeftRadius: 12,
                      borderBottomRightRadius: 12,
                      padding: 5,
                      marginBottom: 15,
                      marginLeft: -25,
                    },
                  }}
                />
              );
            }}
            renderInputToolbar={props => (
              <InputToolbar
                {...props}
                containerStyle={{
                  backgroundColor: COLORS.white,
                  borderWidth: 1.5,
                  borderTopWidth: 2,
                  borderColor: COLORS.red,
                  borderTopColor: COLORS.red,
                  borderRadius: 30,
                  justifyContent: 'center',
                  marginHorizontal: 28,
                  marginBottom: 10,
                  // height: 50,
                }}
                primaryStyle={{alignItems: 'center'}}
              />
            )}
            renderSend={props => (
              <Send
                {...props}
                disabled={!props.text}
                containerStyle={[
                  styles.sendButtonStyles,
                  {opacity: !props.text ? 0.5 : 1},
                ]}>
                <SendFill />
              </Send>
            )}
            renderTime={props => (
              <Time
                {...props}
                timeTextStyle={{
                  left: {
                    color: 'rgba(0, 0, 0, 0.46)',
                    fontSize: 10,
                    textAlign: 'right', // or position: 'right',
                    position: 'absolute',
                    bottom: -24,
                    fontFamily: FONTS.Nunito_Light,
                  },
                  right: {
                    color: 'rgba(0, 0, 0, 0.46)',
                    fontSize: 10,
                    textAlign: 'left', // or position: 'right',
                    position: 'absolute',
                    bottom: -24,
                    right: 0,
                    fontFamily: FONTS.Nunito_Light,
                  },
                }}
              />
            )}
          />
        </>
      )}
    </SafeAreaView>
  );
};

export default ChatDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  sendButtonStyles: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    position: 'absolute',
    bottom: 4,
    right: 10,
  },
  header: {
    flexDirection: 'row',
    paddingLeft: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  name: {
    fontSize: 16,
    marginLeft: 9,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  heading: {
    textAlign: 'center',
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
    paddingTop: 200,
  },
});
