import {
  <PERSON><PERSON><PERSON>iew,
  StatusBar,
  StyleSheet,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import React, {FC} from 'react';
import {COLORS, FONTS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import RenderHTML from 'react-native-render-html';

type Props = NativeStackScreenProps<HomeStackParamList, 'PrivacyPolicy'>;

const PrivacyPolicy: FC<Props> = () => {
  const {width} = useWindowDimensions();

  const privacyPolicyHtml = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Privacy Policy - OW</title>
      <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
      <style>
          body {
              font-family: 'Nunito', sans-serif;
              line-height: 1.6;
              margin: 0;
              padding: 20px;
              background-color: #f9f9f9;
              color: #333;
          }
          h1, h2, h3 {
              color: #222;
          }
          h1 {
              font-size: 2em;
              margin-bottom: 20px;
          }
          h2 {
              font-size: 1.5em;
              margin-top: 20px;
              margin-bottom: 10px;
          }
          p {
              margin-bottom: 15px;
          }
          ul {
              margin-left: 20px;
          }
          ul li {
              margin-bottom: 10px;
          }
          a {
              color: #007bff;
              text-decoration: none;
          }
          a:hover {
              text-decoration: underline;
          }
      </style>
  </head>
  <body>
      <h1>Privacy Policy</h1>
      <p>Welcome to OW! We are committed to protecting your personal information and your right to privacy. This Privacy Policy explains how we collect, use, and disclose your data when you use our app.</p>
      <h2>1. Information We Collect</h2>
      <p>We collect the following types of information:</p>
      <ul>
          <li><strong>Personal Information:</strong> Name, email address, phone number, and other contact details.</li>
          <li><strong>Usage Data:</strong> Information about how you use the app, including log data, device information, and analytics.</li>
          <li><strong>Payment Information:</strong> Transaction data processed via secure payment gateways.</li>
      </ul>
      <h2>2. How We Use Your Information</h2>
      <p>We use your information to:</p>
      <ul>
          <li>Provide, maintain, and improve our services.</li>
          <li>Facilitate bookings and communication between users.</li>
          <li>Ensure compliance with our terms and policies.</li>
          <li>Send you important updates and notifications.</li>
      </ul>
      <h2>3. Sharing of Information</h2>
      <p>We do not sell or rent your personal information. We may share data with:</p>
      <ul>
          <li><strong>Service Providers:</strong> To support app functionality and process payments.</li>
          <li><strong>Law Enforcement:</strong> If required by law or to protect our legal rights.</li>
          <li><strong>Business Transfers:</strong> In the event of a merger, acquisition, or sale of assets.</li>
      </ul>
      <h2>4. Data Security</h2>
      <p>We use industry-standard security measures to protect your information. However, no method of transmission over the Internet or electronic storage is 100% secure.</p>
      <h2>5. Your Privacy Rights</h2>
      <p>You have the right to:</p>
      <ul>
          <li>Access your personal information.</li>
          <li>Request corrections or updates to your data.</li>
          <li>Delete your account and associated data.</li>
      </ul>
      <h2>6. Cookies and Tracking Technologies</h2>
      <p>We use cookies and similar technologies to enhance your experience. You can disable cookies through your browser settings.</p>
      <h2>7. Changes to This Privacy Policy</h2>
      <p>We may update this Privacy Policy from time to time. Significant changes will be communicated via app notifications or email.</p>
      <h2>8. Contact Us</h2>
      <p>If you have any questions about this Privacy Policy, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
  </body>
  </html>
  `;

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      <ScrollView
        contentContainerStyle={{paddingBottom: 32}}
        showsVerticalScrollIndicator={false}>
        <RenderHTML
          baseStyle={{color: 'black'}} // Ensures all text remains black
          contentWidth={width}
          source={{html: privacyPolicyHtml}}
        />
      </ScrollView>
    </View>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 10,
    paddingHorizontal: 20,
  },
});
