import { ActivityIndicator, FlatList, StatusBar, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { appStyles, COLORS, FONTS } from "../../../themes";
import { ProgramItem } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { useFocusEffect } from "@react-navigation/native";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { firebase } from "@react-native-firebase/firestore";
import { IProgram, IUserProgram } from "../../../models/IProgram";
import { useAppSelector } from "../../../redux/config/Store";
import MyProgramTabs from "./MyProgramTabs";

type Props = NativeStackScreenProps<HomeStackParamList, "MyProgram">;

const MyProgram: React.FC<Props> = ({ navigation }) => {
  const userId = useAppSelector((state) => state.auth.userId);
  const [programs, setPrograms] = useState<IProgram[]>([]);
  const [userProgram, setUserProgram] = useState<IUserProgram[]>([]);
  const [assignedPrograms, setAssignedPrograms] = useState<IProgram[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"program" | "joined">("program");

  const fetchPrograms = async () => {
    try {
      let query = firebase.firestore().collection("Programs").where("subscriber", "array-contains", userId);
      const snapshot = await query.get();
      const data = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as IProgram[];
      setPrograms(data);
    } catch (err) {
      console.log(err);
    }
  };

  const fetchUserPrograms = () => {
    try {
      const query = firebase.firestore().collection("UserPrograms").where("userId", "==", userId);

      // Use onSnapshot to listen for real-time updates
      const unsubscribe = query.onSnapshot((snapshot) => {
        const data = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as IUserProgram[];
        setUserProgram(data);
      });

      // Optionally, return the unsubscribe function to stop listening
      return unsubscribe;
    } catch (err) {
      console.log(err);
    }
  };
  const fetchAssignedPrograms = () => {
    try {
      const query = firebase.firestore().collection("AssignedPrograms").where("patientId", "==", userId);

      // Use onSnapshot to listen for real-time updates
      const unsubscribe = query.onSnapshot((snapshot) => {
        const data = snapshot.docs.map((doc) => {
          const assignedData = doc.data();
          const programData = assignedData.programData as IProgram;

          // Calculate percentage based on completed exercises in the assigned program
          const completedExercises = programData.completedExercises || [];
          const totalExercises = programData.exercises?.length || 0;
          const percentage = totalExercises > 0 ? (completedExercises.length / totalExercises) * 100 : 0;

          return {
            ...programData,
            percentage: percentage || 0,
          };
        });
        setAssignedPrograms(data);
        setLoading(false);
      });

      return unsubscribe;
    } catch (error) {
      console.error("Error fetching assigned programs:", error);
      setLoading(false);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await Promise.all([fetchPrograms(), fetchUserPrograms()]);
      } catch (err) {
        console.log(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const updateProgramsWithPercentage = (programs: IProgram[], userProgram: IUserProgram[]) => {
    return programs.map((program) => {
      const userProg = userProgram.find((userProg) => userProg.programId === program.id);
      if (userProg) {
        const percentage =
          program.exercises.length > 0 ? (userProg.completedExercises.length / program.exercises.length) * 100 : 0;

        return {
          ...program,
          percentage: percentage || 0, // Ensure percentage is never undefined or NaN
        };
      }
      return program;
    });
  };

  // Use useMemo to avoid unnecessary re-renders
  const updatedPrograms = useMemo(() => {
    if (activeTab === "joined") {
      // For assigned programs, return them as they already have percentage calculated
      return assignedPrograms;
    } else if (programs.length && userProgram.length) {
      // For regular programs, calculate percentage using UserPrograms collection
      return updateProgramsWithPercentage(programs, userProgram);
    }
    return programs;
  }, [programs, userProgram, assignedPrograms, activeTab]);

  // Use useFocusEffect to refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // This will run every time the screen comes into focus
      // The onSnapshot listeners will automatically update the data
      // No need to manually refetch as the listeners handle real-time updates
      return () => {
        // Cleanup if needed when screen loses focus
      };
    }, []),
  );

  useEffect(() => {
    let unsubscribeUserPrograms: (() => void) | undefined;
    let unsubscribeAssignedPrograms: (() => void) | undefined;

    if (activeTab === "program") {
      fetchPrograms();
      unsubscribeUserPrograms = fetchUserPrograms();
    } else if (activeTab === "joined") {
      setLoading(true);
      unsubscribeAssignedPrograms = fetchAssignedPrograms();
    }

    // Cleanup function to unsubscribe from listeners
    return () => {
      if (unsubscribeUserPrograms) {
        unsubscribeUserPrograms();
      }
      if (unsubscribeAssignedPrograms) {
        unsubscribeAssignedPrograms();
      }
    };
  }, [activeTab]);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />
      <MyProgramTabs activeTab={activeTab} setActiveTab={setActiveTab} />
      {loading ? (
        <View style={appStyles.loadingContainer}>
          <Text style={appStyles.loadingParagraph}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={updatedPrograms}
          contentContainerStyle={{
            gap: 16,
            paddingBottom: 34,
          }}
          ListHeaderComponent={<Text style={styles.title}>Continue Watching</Text>}
          ListEmptyComponent={
            <View style={appStyles.loadingContainer}>
              <Text style={appStyles.loadingParagraph}>No Video found</Text>
            </View>
          }
          showsVerticalScrollIndicator={false}
          renderItem={({ item, index }) => {
            return (
              <ProgramItem
                imageUrl={item.thumbnail}
                duration={item.duration}
                numberofExercises={item?.exercises?.length}
                name={item.name}
                type={item.type}
                showButton={true}
                onPressProgramItem={() =>
                  navigation.navigate("ProgramDetails", {
                    programId: item.id,
                    isAssigned: activeTab === "joined",
                  })
                }
                containerStyle={{
                  marginHorizontal: 4,
                  marginBottom: updatedPrograms.length - 1 == index ? 4 : 0,
                }}
                isMyCourse={activeTab === "joined" ? true : item.subscriber.includes(userId)}
                showPercentage
                percentage={item.percentage}
              />
            );
          }}
        />
      )}
    </View>
  );
};

export default MyProgram;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 24,
    fontFamily: FONTS.Nunito_Bold,
    paddingLeft: 2,
    marginBottom: 4,
  },
});
