import { ImageBackground, SafeAreaView, ScrollView, StyleSheet, Text, View } from "react-native";
import React from "react";
import { COLORS, FONTS, sizes } from "../../../themes";

import { ButtonwithIcon, TextButtonwithIcon } from "../../../components";

import { ArrowLeft, ArrowLeft1, Edit3, Location, Star1 } from "../../../assets/svgicons";
import { Dr } from "../../../assets/images";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import { setOfferServicesAction } from "../../../redux/slices/OfferServicesSlice";
type Props = NativeStackScreenProps<ProfessionalStackParamList, "EditDoctorDetails">;

const EditDoctorDetails: React.FC<Props> = ({ navigation }) => {
  const { user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  const [isModalVisible, setModalVisible] = React.useState(false);
  const [isScheduleModalVisible, setIsScheduleModalVisible] = React.useState(false);

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleModalScedule = () => {
    setIsScheduleModalVisible(!isScheduleModalVisible);
  };

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
      <SafeAreaView style={styles.container}>
        {/* PROFILE_IMAGE */}
        <ImageBackground resizeMode="cover" source={{ uri: user?.photo }} style={styles.imageContainer}>
          {/* BACK_ARROW_BUTTON */}

          <ButtonwithIcon
            icon={<ArrowLeft1 fill={COLORS.white} width={24} height={24} />}
            containerStyle={styles.backArrowContainer}
            onPress={() => navigation.goBack()}
          />
          <ButtonwithIcon
            icon={<Edit3 height={20} width={20} />}
            containerStyle={{ margin: 20 }}
            onPress={() =>
              navigation.navigate("UpdateProfilePicture", {
                userId: user._id,
                isProfile: true,
              })
            }
          />
        </ImageBackground>
        <View
          style={{
            marginTop: 18,
            paddingHorizontal: 20,
          }}
        >
          <View style={styles.iconContainer}>
            {/* NAME */}
            <Text style={styles.name}>{user?.name}</Text>
            <ButtonwithIcon
              icon={<Edit3 height={20} width={20} />}
              onPress={() => navigation.navigate("EditDoctorInfo")}
            />
          </View>
          {/* ADDRES */}
          <TextButtonwithIcon
            leftIcon={<Location />}
            label={user?.address || "No address added"}
            labelStyle={styles.address}
            containerStyle={{ marginTop: 6 }}
            disabled
          />
          {/* RATING_AND_INFO_CONTAINER */}
          <View style={{ marginTop: 12, flexDirection: "row" }}>
            <View style={styles.info}>
              <Text style={styles.infoText}>Therapist</Text>
            </View>
            {/* RATING */}
            {user?.avgRating ? (
              <TextButtonwithIcon
                label={user?.avgRating?.toString()}
                labelStyle={styles.ratingText}
                rightIcon={<Star1 />}
                containerStyle={styles.ratingContainer}
                disabled
              />
            ) : null}
          </View>
          <View style={styles.iconContainer}>
            {/* ABOUT_DOCTOR */}
            <Text style={styles.subTitle}>About Doctor</Text>
            <ButtonwithIcon
              icon={<Edit3 height={20} width={20} />}
              onPress={() =>
                navigation.navigate("EditIntroduction", {
                  userId: user._id,
                  isProfile: true,
                })
              }
            />
          </View>

          {/* DISCRIPTION */}
          <Text style={styles.paragraph}>{user?.introduction}</Text>
        </View>
        <View style={styles.footer}>
          <View style={styles.iconContainer}>
            {/* APPOINTMENT */}
            <Text style={[styles.subTitle, { marginTop: 0 }]}>Appointments</Text>
            <ButtonwithIcon
              icon={<Edit3 height={20} width={20} />}
              onPress={() => {
                dispatch(setOfferServicesAction(user?.services));
                navigation.navigate("UpdateServicesList", {
                  userId: user._id,
                  isProfile: true,
                });
              }}
            />
          </View>
          {/* APPOINTED_CARD */}
          <View style={{ marginBottom: 40 }}>
            {user?.services?.length
              ? user?.services?.map((item, i) => (
                  <View key={i} style={styles.card}>
                    <Text style={[styles.title, { marginTop: 14 }]}>{item?.name}</Text>
                    <Text style={[styles.text, { marginTop: 8 }]}>1 hour @ ${item?.price}</Text>
                  </View>
                ))
              : null}
          </View>
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default EditDoctorDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  imageContainer: {
    height: 280,
    width: "100%",
    // alignItems: 'flex-start',
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  address: {
    fontSize: 10,
    color: "#C7C7C7",
    marginLeft: 5,
    fontFamily: FONTS.Nunito_Regular,
  },
  info: {
    width: 90,
    height: 30,
    backgroundColor: "#EB4E1F20",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 30 / 2,
  },
  infoText: {
    fontSize: 10,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingText: {
    fontSize: 14,
    color: "#FCBD18",
    marginRight: 6,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingContainer: {
    width: 60,
    height: 30,
    backgroundColor: "#FCBD1830",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 60 / 2,
    marginLeft: 11,
  },
  subTitle: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 21,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 12,
    color: "#ABABAB",
    marginTop: 11,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  appointmentButton: {
    height: 50,
    borderRadius: 50 / 2,
    backgroundColor: COLORS.red,
    marginTop: 25,
  },
  appointmentModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scheduleModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  iconContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  card: {
    backgroundColor: "#FBFBFB",
    padding: 10,
    borderRadius: 10,
    marginTop: 10,
  },
  text: {
    fontSize: 11,
    color: "#263238",
    fontFamily: FONTS.Nunito_Regular,
  },
  title: {
    fontSize: 15,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  backArrowContainer: {
    margin: 22,
    backgroundColor: "#00000040",
    width: 32,
    height: 32,
    borderRadius: 4,
  },
});
