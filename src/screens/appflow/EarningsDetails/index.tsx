import { StatusBar, StyleSheet, Text, View, ScrollView } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import { Chart, TextButton, TextButtonwithIcon } from "../../../components";
import { DropDown1 } from "../../../assets/svgicons";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { formatPrice } from "../../../utilities/app.utils";
import moment from "moment";
import { fetchActiveBookingsForEarning, fetchCurrentPhaseEarnings } from "../../../redux/slices/EarningSlice";
import FilterModal from "../../../components/EarningModal/EarningModal";
import EarningService from "../../../Services/EarningService";
import { IAccountBalance } from "../../../models/IEarning";
import ConfirmModal from "../../../components/ConfirmModal/ConfirmModal";
import Toast from "react-native-toast-message";
type Props = NativeStackScreenProps<ProfessionalStackParamList, "EarningsDetails">;

const EarningsDetails: React.FC<Props> = () => {
  const dispatch = useAppDispatch();
  const [isModalVisible, setModalVisible] = useState(false);
  const [filterString, setFilterString] = useState<string>("Last Month");

  const { meta: previousMonthMeta } = useAppSelector((state) => state.earnings.previousMonth);
  const { results: currentPhaseResults, meta: currentPhaseMeta } = useAppSelector(
    (state) => state.earnings.currentPhase,
  );
  const { meta: activeBookingMeta } = useAppSelector((state) => state.earnings.activeBooking);
  const { userId } = useAppSelector((state) => state.auth);

  const chart_data = currentPhaseResults.map((item, index) => ({
    label: moment(item.createdAt).format("DD"),
    value: +formatPrice(item.service.price),
  }));

  const [balance, setBalance] = useState<null | IAccountBalance>(null);
  const [isWithdrawVisible, setWidthdrawVisible] = useState<boolean>(false);
  const [isWithdrawLoading, setWithdrawLoading] = useState<boolean>(false);

  async function getAccountBalance() {
    await EarningService.GetAccountBalance(userId)
      .then((res) => {
        setBalance(res.data);
      })
      .catch((error) => console.log("GET ACCOUNT BALANCE >> ", error));
  }

  async function onWithdrawAmount() {
    setWithdrawLoading(true);
    const payload = { doctorId: userId, amount: balance.available[0].amount, currency: "usd" };
    await EarningService.TakeWidthdraw(payload)
      .then(() => {
        Toast.show({ type: "success", text2: "Your transaction has been successful", text1: "Success" });
      })
      .catch((error) => {
        console.log("TAKE WITHDRAW >> ", error.response.data.meta.message);
        Toast.show({ type: "error", text2: error.response.data.meta.message, text1: "Error" });
      })
      .finally(() => {
        setWithdrawLoading(false);
        setWidthdrawVisible(false);
      });
  }

  useEffect(() => {
    if (userId) {
      getAccountBalance();
      dispatch(fetchActiveBookingsForEarning(userId));
    }
  }, []);

  // Add new useEffect for filterString changes
  useEffect(() => {
    if (userId) {
      let startDate, endDate;

      switch (filterString) {
        case "Last Month":
          startDate = moment().subtract(1, "month").startOf("month").format("YYYY-MM-DD");
          endDate = moment().endOf("month").format("YYYY-MM-DD");
          break;
        case "Last 3 Months":
          startDate = moment().subtract(3, "months").startOf("month").format("YYYY-MM-DD");
          endDate = moment().endOf("month").format("YYYY-MM-DD");
          break;
        case "Last 6 Months":
          startDate = moment().subtract(6, "months").startOf("month").format("YYYY-MM-DD");
          endDate = moment().endOf("month").format("YYYY-MM-DD");
          break;
        case "Last Year":
          startDate = moment().subtract(1, "year").startOf("year").format("YYYY-MM-DD");
          endDate = moment().endOf("year").format("YYYY-MM-DD");
          break;
        default:
          startDate = moment().subtract(1, "month").startOf("month").format("YYYY-MM-DD");
          endDate = moment().endOf("month").format("YYYY-MM-DD");
      }

      const payload = {
        doctorId: userId,
        startDate,
        endDate,
      };

      dispatch(fetchCurrentPhaseEarnings(payload));
    }

    return () => {
      const payload = {
        doctorId: userId,
        startDate: moment().startOf("month").format("YYYY-MM-DD"),
        endDate: moment().endOf("month").format("YYYY-MM-DD"),
      };
      dispatch(fetchCurrentPhaseEarnings(payload));
    };
  }, [filterString, userId]);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} />
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 16 }}>
        {/* WITH_DRAW_REQUEST */}
        <View style={styles.withDrawContainer}>
          {/* PRICE */}
          <Text style={styles.priceText}>$ {balance ? balance.available[0]?.amount.toFixed(2) : (0).toFixed(2)}</Text>
          <Text style={styles.availableText}>Available for withdrawals</Text>
          {/* REQUEST_WITH_DRAW_BUTTON */}
          <TextButton
            label={"Request Withdraw"}
            containerStyle={styles.requestWithDrawButton}
            disabled={!balance?.available[0]?.amount}
            onPress={() => setWidthdrawVisible(true)}
          />
        </View>
        {/* LAST_SIX_MONTH_REPORT */}
        <View style={styles.lastSixMonth}>
          <View>
            <Text style={styles.price1}>$ {formatPrice(currentPhaseMeta.totalEarnings)}</Text>
            {/* <Text style={styles.monthText}>This Month</Text> */}
          </View>
          <TextButtonwithIcon
            label={filterString}
            labelStyle={{ fontSize: 12, color: COLORS.red, marginRight: 5 }}
            rightIcon={<DropDown1 />}
            onPress={() => setModalVisible(true)}
          />
        </View>
        {/* CHART */}
        <View style={styles.line} />
        {chart_data.length ? <Chart data={chart_data} /> : null}
        {/* TITLE */}
        <Text style={styles.title}>Analytics</Text>
        {/* ANALYTICS */}
        <View style={styles.analyticsCard}>
          <Text style={styles.subTitle}>Earnings in {moment().subtract(1, "month").format("MMMM")}</Text>
          <Text style={styles.price2}>$ {formatPrice(previousMonthMeta?.totalEarnings)}</Text>
          <View style={styles.line} />
          <Text style={[styles.subTitle, { paddingTop: 20 }]}>Active Bookings</Text>
          <Text style={styles.price2}>
            {activeBookingMeta.activeBookings}{" "}
            <Text style={{ color: "#838383" }}>($ {formatPrice(activeBookingMeta.activeBookingsEarning)})</Text>
          </Text>
          <View style={styles.line} />
          <Text style={[styles.subTitle, { paddingTop: 20 }]}>Available for withdrawal</Text>
          <Text style={[styles.price2, { color: COLORS.red }]}>
            $ {balance ? balance.available[0]?.amount.toFixed(2) : 0.0}
          </Text>
          <View style={styles.line} />
        </View>
      </ScrollView>
      <FilterModal
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        filterString={filterString}
        setFilterString={setFilterString}
      />

      {/* CONFIRMATION MODAL */}
      <ConfirmModal
        isVisible={isWithdrawVisible}
        message="Are sure to withdraw all of your balance?"
        onClose={() => setWidthdrawVisible(false)}
        onConfirm={onWithdrawAmount}
        title="Request Withdraw"
        cancelLabel="Cancel"
        confirmLabel="Confirm"
        loading={isWithdrawLoading}
      />
    </View>
  );
};

export default EarningsDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  withDrawContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    borderWidth: 1.3,
    borderColor: "#F4F4F4",
    alignItems: "center",
    paddingTop: 32,
    paddingBottom: 18,
    paddingHorizontal: 18,
    marginHorizontal: 20,
  },
  priceText: {
    fontSize: 24,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_Bold,
  },
  availableText: {
    fontSize: 12,
    color: "#626363",
    paddingTop: 5,
    fontFamily: FONTS.Nunito_Regular,
  },
  requestWithDrawButton: {
    height: 50,
    backgroundColor: COLORS.red,
    width: "100%",
    borderRadius: 10,
    marginTop: 30,
  },
  lastSixMonth: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: 35,
    alignItems: "center",
  },
  price1: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  monthText: {
    fontSize: 12,
    color: "#D5D6D7",
    fontFamily: FONTS.Nunito_Medium,
  },
  line: {
    height: 1,
    backgroundColor: "#EAEBEB",
    marginVertical: 8,
  },
  analyticsCard: {
    padding: 20,
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: "#F4F4F4",
    marginHorizontal: 20,
    borderRadius: 10,
    marginTop: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.Nunito_Bold,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  subTitle: {
    fontSize: 12,
    color: "#292D32",
    fontFamily: FONTS.Nunito_SemiBold,
  },
  price2: {
    fontSize: 16,
    color: "#292D32",
    fontFamily: FONTS.Nunito_SemiBold,
    paddingTop: 5,
  },
});
