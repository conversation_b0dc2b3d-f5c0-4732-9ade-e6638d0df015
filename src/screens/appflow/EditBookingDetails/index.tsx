import { StyleSheet, Text, View, TouchableOpacity, Alert } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import Modal from "react-native-modal";
import moment from "moment";
import { AppButton, AppointmentSchdule, ChooseAppointment, TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import BookingService from "../../../Services/BookingService";
import { AxiosError } from "axios";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import { addBookingAction } from "../../../redux/slices/BookingsSlice";
import { IOfferService } from "../../../models/IOfferService";
import { usePaymentSheet } from "@stripe/stripe-react-native";
import AppInstance from "../../../config/axiosInstance";
import Toast from "react-native-toast-message";
import useApiHandler from "../../../Hooks/useApiHandler";
import PaymentServices from "../../../Services/PaymentServices";
import { IBooking } from "../../../models/IBooking";
import { PUBLISHABLE_KEY } from "@env";

type Props = NativeStackScreenProps<HomeStackParamList, "EditBookingDetails">;

const customAppearance = {
  shapes: {
    borderRadius: 12,
    borderWidth: 0.5,
  },
  primaryButton: {
    shapes: {
      borderRadius: 20,
    },
  },
  colors: {
    primary: COLORS.primary,
    background: COLORS.white,
    componentBackground: "#f3f8fa",
    componentBorder: "#f3f8fa",
    componentDivider: "#000000",
    primaryText: "#000000",
    secondaryText: "#000000",
    componentText: "#000000",
    placeholderText: "#73757b",
  },
};

type PaymentSheetParams = {
  clientSecret: string;
  ephemeralKey: string;
  customer: string;
  booking: IBooking;
};

const EditBookingDetails: React.FC<Props> = ({ navigation, route }) => {
  const { userId, user } = useAppSelector((state) => state.auth);
  const { bookingData, shouldGoBack } = route.params;

  const dispatch = useAppDispatch();
  const { handleApiErrors } = useApiHandler();

  const [isLoading, setLoading] = React.useState(false);
  const [isModalVisible, setModalVisible] = React.useState(false);
  const [isScheduleModalVisible, setIsScheduleModalVisible] = useState(false);
  const [paymentId, setPaymentId] = useState("");
  const [initialLoading, setInitialLoading] = useState(false);

  const { initPaymentSheet, presentPaymentSheet } = usePaymentSheet();

  // useEffect(() => {
  //   console.log(" key  >> ", PUBLISHABLE_KEY);
  // }, []);

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleModalScedule = () => {
    setIsScheduleModalVisible(!isScheduleModalVisible);
  };

  async function createBooking(): Promise<PaymentSheetParams | null> {
    setLoading(true);
    const payload = {
      doctor: bookingData?.doctor?._id,
      patient: userId,
      service: {
        ...bookingData?.service,
        sessionTime: 60,
      },
      bookingDateTime: bookingData.bookingDateTime,
    };

    const response: PaymentSheetParams | null = await BookingService.CreateBooking(payload)
      .then(({ data }) => {
        const { clientSecret, ephemeralKey, booking } = data;
        dispatch(addBookingAction(booking));
        return { clientSecret, ephemeralKey, booking, customer: user.stripeCustomerId } as PaymentSheetParams;
      })
      .catch((error) => {
        setLoading(false);
        console.error("Create Booking Error:", error?.response?.data?.meta?.message);
        if (error?.response?.data?.meta?.message === "Slot not available for the given date") {
          Toast.show({ type: "error", text2: "Select time slot is not available" });
        } else {
          Toast.show({ type: "error", text2: "Error while creating a booking" });
        }
        return null;
      });
    return response;
  }

  const initializePaymentSheet = async () => {
    const res = await createBooking();

    if (res !== null) {
      const { error } = await initPaymentSheet({
        customerId: res.customer,
        customerEphemeralKeySecret: res.ephemeralKey,
        paymentIntentClientSecret: res.clientSecret,
        merchantDisplayName: `${user.name}`,
        allowsDelayedPaymentMethods: true,
        appearance: customAppearance,
        returnURL: "yourapp://payment-success",
      }).finally(() => setInitialLoading(false));

      if (error) {
        setLoading(false);
        console.error("Payment Sheet Init Error:", error);
        Toast.show({ type: "error", text2: "Error initializing payment" });
        await BookingService.DeleteBooking(res.booking._id);
        return;
      }

      handlesheet(res.booking);
    }
  };

  const handlesheet = async (booking: IBooking) => {
    try {
      const { error } = await presentPaymentSheet();
      setLoading(false);
      if (error) {
        console.error("Payment Sheet Error:", error);
        if (error.code === "Canceled") {
          console.log("Payment was canceled by user");
        } else {
          Toast.show({
            type: "error",
            text2: error.localizedMessage || "Payment failed",
          });
        }
        BookingService.DeleteBooking(booking._id);
        return;
      }

      // PAYMENT SUCCESSFULL
      if (shouldGoBack) {
        navigation.pop(2);
      } else {
        navigation.replace("BookingList");
      }
    } catch (error) {
      console.error("Payment Sheet Exception:", error);
      Toast.show({ type: "error", text2: "Unexpected payment error" });
      await BookingService.DeleteBooking(booking._id);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>You are Confirming Appointment with:</Text>

      <View style={styles.doctorInfoContainer}>
        <View style={styles.lineStyles} />
        <View style={{ flex: 1 }}>
          <View style={styles.nameAndBtnContainer}>
            <Text style={styles.name}>{bookingData?.doctor?.name}</Text>
            {/* <TextButton
              label={'Edit'}
              labelStyle={{color: COLORS.red}}
              onPress={() => navigation.goBack()}
            /> */}
          </View>
          <Text style={styles.docType}>Therapist</Text>
        </View>
      </View>

      {/* TODAY_APPOINTMENT */}
      <View style={styles.appointmentContainer}>
        <Text style={styles.appointmentText}>Appointments</Text>
        <TextButton label={"Edit"} labelStyle={{ color: COLORS.red }} onPress={toggleModal} />
      </View>
      <TouchableOpacity style={[styles.appointmentDetails]}>
        <Text style={styles.speciality}>{bookingData?.service?.name}</Text>
        <Text style={styles.timeText}>1 hour @ ${bookingData?.service?.price}</Text>
      </TouchableOpacity>

      {/* DATE_&_TIME */}
      <View style={{ paddingTop: 50 }}>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text style={styles.dateText}>Date & Time:</Text>
          <TextButton label={"Edit"} labelStyle={{ color: COLORS.red }} onPress={toggleModalScedule} />
        </View>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Text style={styles.text}>{moment(bookingData?.bookingDateTime?.date).format("MMM DD, dddd")}</Text>
          {/* DOT */}
          <View style={styles.dot} />
          <Text style={styles.text}>
            <Text style={styles.text}>{bookingData?.bookingDateTime?.time}</Text>
          </Text>
        </View>
      </View>
      <View style={styles.footer}>
        <AppButton
          title="Book Appointment"
          disabled={isLoading || initialLoading}
          isLoading={isLoading || initialLoading}
          onPress={initializePaymentSheet}
          containerStyle={{ marginHorizontal: 0 }}
        />
        {/* <TextButton
          label={'Book Appointment'}
          containerStyle={styles.appointmentButton}
          disabled={isLoading}
          isLoading={isLoading}
          onPress={
            createBooking
            // navigation.navigate('HomePaymentMethod', {bookingData})
          }
        /> */}
        <Text style={styles.paragraph}>
          Lorem ipsum dolor sit amet consectetur. Eget varius est posuere augue cursus suspendisse.
        </Text>
      </View>

      {/* CHOOSE SERVICE */}
      <Modal
        isVisible={isModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModal()}
        style={styles.appointmentModalContainer}
      >
        <View>
          <ChooseAppointment
            services={bookingData.doctor.services}
            currentService={bookingData?.service}
            onContinuePress={(v) => {
              navigation.setParams({
                bookingData: {
                  ...bookingData,
                  service: v,
                },
              });
              toggleModal();
            }}
          />
        </View>
      </Modal>

      {/* SCHEDULE MODAL */}
      <Modal
        isVisible={isScheduleModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModalScedule()}
        style={styles.scheduleModalContainer}
      >
        <View>
          <AppointmentSchdule
            schedule={bookingData.doctor.schedule}
            selectedAppointment={bookingData.selectedAppointment}
            doctorId={bookingData.doctor._id}
            currentSchedule={bookingData?.bookingDateTime}
            onPress={(v) => {
              navigation.setParams({
                bookingData: {
                  ...bookingData,
                  bookingDateTime: v,
                },
              });
              toggleModalScedule();
            }}
          />
        </View>
      </Modal>
    </View>
  );
};

export default EditBookingDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.Nunito_Bold,
    color: COLORS.black,
  },
  lineStyles: {
    height: 33,
    backgroundColor: COLORS.red,
    width: 12,
    marginRight: 5,
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    lineHeight: 20,
  },
  docType: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Regular,
    lineHeight: 15,
  },
  nameAndBtnContainer: {
    flexDirection: "row",
    flex: 1,
    justifyContent: "space-between",
  },
  doctorInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 18,
  },
  appointmentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 40,
    paddingBottom: 16,
  },
  appointmentText: {
    fontSize: 16,
    color: "#030F1C",
    fontFamily: FONTS.Nunito_Bold,
  },
  appointmentDetails: {
    backgroundColor: "#FF3D0010",
    paddingVertical: 20,
    paddingLeft: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.red,
  },
  dateText: {
    fontSize: 16,
    color: "#333333",
    marginBottom: 4,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  text: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Regular,
  },
  dot: {
    width: 7,
    height: 7,
    backgroundColor: "#D9D9D9",
    borderRadius: 7 / 2,
    marginHorizontal: 4,
  },
  cancleButton: {
    width: "25%",
    height: 20,
  },
  innerContainer: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
  },
  footer: {
    flex: 1,
    paddingTop: 60,
  },
  appointmentButton: {
    height: 50,
    backgroundColor: COLORS.red,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 14,
    elevation: 17,
  },
  paragraph: {
    fontSize: 12,
    color: "#AAAAAA",
    textAlign: "center",
    paddingTop: 25,
    fontFamily: FONTS.Nunito_Regular,
  },
  appointmentModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scheduleModalContainer: {
    margin: 0,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  speciality: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  timeText: {
    fontSize: 12,
    color: "#404040",
    fontFamily: FONTS.Nunito_Light,
    paddingTop: 8,
  },
});
