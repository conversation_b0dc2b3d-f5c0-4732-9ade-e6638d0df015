import { ActivityIndicator, StatusBar, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { FC, useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { WebView } from "react-native-webview";
import PaymentServices from "../../../Services/PaymentServices";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import { ArrowLeft } from "../../../assets/svgicons";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { getLoggedInUser } from "../../../redux/slices/AuthSlice";

type Props = NativeStackScreenProps<ProfessionalStackParamList, "ConnectedAccount">;

const ConnectedAccount: FC<Props> = ({ navigation }) => {
  const [doctorId, setDoctorId] = useState("");
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [onboardingUrl, setOnboardingUrl] = useState("");
  const { userId } = useAppSelector((state) => state.auth);
  const disptach = useAppDispatch();

  const createConnectedAccount = async () => {
    setLoading(true);
    setError("");

    try {
      const response = await PaymentServices.CreateConnectedAccount();
      const { data: accountId } = response;

      // Verify we have a valid account ID before proceeding
      if (accountId && typeof accountId === "string") {
        // setDoctorId(accountId);
        createOnboardingLink(accountId);
      }
    } catch (err) {
      console.error("Account creation error:", err);
      setError("Failed to create account. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const createOnboardingLink = async (accountId: string) => {
    if (!accountId) return;
    setLoading(true);
    setError("");
    try {
      const response = await PaymentServices.CreateOnboardingLink(accountId);

      if (!response.data?.url) {
        throw new Error("Invalid response: missing onboarding URL");
      }
      setOnboardingUrl(response.data.url);
      // await Linking.canOpenURL(response.data.url);
      // Linking.openURL(response.data.url);
    } catch (err) {
      console.error("Onboarding link error:", err.response.data);
      setError("Failed to generate onboarding link. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // HANLDE SCREEN ON GO BACK
  function onGoBack() {
    navigation.goBack();
    if (userId) {
      disptach(getLoggedInUser(userId));
    }
  }

  useEffect(() => {
    createConnectedAccount();
    navigation.setOptions({
      headerShown: true,

      headerLeft: () => (
        <>
          <TouchableOpacity onPress={onGoBack} hitSlop={10} style={{ width: 30, marginLeft: 4 }}>
            <ArrowLeft fill="#000" />
          </TouchableOpacity>
        </>
      ),
    });
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      {isLoading && (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <ActivityIndicator size="large" color={COLORS.red} />
          <Text style={{ marginTop: 10, fontFamily: FONTS.Nunito_Regular }}>Loading...</Text>
        </View>
      )}

      {!isLoading && onboardingUrl.trim() ? (
        <WebView
          style={{ flex: 1 }}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          thirdPartyCookiesEnabled={true}
          sharedCookiesEnabled={true}
          source={{
            uri: onboardingUrl,
            headers: {
              "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Chrome/91.0.4472.124",
            },
          }}
        />
      ) : null}
    </View>
  );
};

export default ConnectedAccount;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    backgroundColor: COLORS.red,
    height: 80,
    borderTopRightRadius: 80 / 2,
    borderBottomRightRadius: 80 / 2,
    marginTop: 15,
    justifyContent: "center",
    paddingLeft: 20,
  },
  headerTitle: {
    fontSize: 20,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_ExtraBold,
  },
  headerParagraph: {
    fontSize: 12,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Regular,
  },
  sectionHeader: {
    marginTop: 20,
  },
  title: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
});
