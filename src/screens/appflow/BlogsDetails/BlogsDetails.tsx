import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import RenderHtml from 'react-native-render-html';
import React from 'react';
import {COLORS, FONTS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
type Props = NativeStackScreenProps<HomeStackParamList, 'BlogsDetails'>;

const BlogsDetails: React.FC<Props> = ({route}) => {
  const {title, about, thumbnail, blogContent} = route.params.blog;

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{paddingBottom: 32}}
        showsVerticalScrollIndicator={false}>
        {/* TITLE */}
        <Text style={styles.title}>{title}</Text>
        {/* PARAGRAPH */}
        <Text style={styles.paragraph}>{about}</Text>
        {/* IMAGE */}
        <Image source={{uri: thumbnail}} style={styles.imageContainer} />
        {/* PARAGRAPH */}
        <View>
          <RenderHtml
            contentWidth={Dimensions.get('screen').width}
            source={{html: blogContent}}
            baseStyle={{color: 'black'}} // Ensures all text remains black
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default BlogsDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  paragraph: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  imageContainer: {
    width: '100%',
    height: 130,
    marginTop: 15,
    borderRadius: 10,
  },
  title: {
    fontSize: 28,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    paddingBottom: 7,
  },
});
