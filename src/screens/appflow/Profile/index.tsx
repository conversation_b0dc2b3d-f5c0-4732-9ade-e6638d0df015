import { Image, ScrollView, StyleSheet, Text, View, TouchableOpacity, FlatList, StatusBar } from "react-native";
import React from "react";
import { COLORS, FONTS } from "../../../themes";
import { Community, person } from "../../../assets/images";
import { ButtonwithIcon, ProfileAction, TextButton, TextButtonwithIcon } from "../../../components";
import {
  ArrowRight,
  Calendar1,
  CrowCircle1,
  Book,
  About,
  Privacy,
  Support,
  Logout,
  Settings1,
} from "../../../assets/svgicons";
import { SocialMediaButton } from "../../../constants";
import Modal from "react-native-modal";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { setLogoutAction } from "../../../redux/slices/AuthSlice";
import { bookingResetAction } from "../../../redux/slices/BookingsSlice";
import ConfirmModal from "../../../components/ConfirmModal/ConfirmModal";
type Props = NativeStackScreenProps<HomeStackParamList, "Profile">;

const Profile: React.FC<Props> = ({ navigation }) => {
  const [isShareModalVisible, setShareModalVisible] = React.useState(false);
  const [isLogoutModalVisible, setLogoutModalVisible] = React.useState(false);
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const toggleShareModal = () => {
    setShareModalVisible(!isShareModalVisible);
  };
  const toggleLogoutModal = () => {
    setLogoutModalVisible(!isLogoutModalVisible);
  };
  const renderShareModal = () => {
    return (
      <Modal
        isVisible={isShareModalVisible}
        onBackdropPress={() => toggleShareModal()}
        backdropOpacity={0.2}
        style={{ margin: 0, position: "absolute", bottom: 0, left: 0, right: 0 }}
      >
        <View style={styles.modalContainer}>
          <FlatList
            data={SocialMediaButton}
            ItemSeparatorComponent={() => <View style={{ height: 38 }} />}
            columnWrapperStyle={{ justifyContent: "flex-start" }}
            numColumns={4}
            renderItem={({ item, index }) => {
              return (
                <TouchableOpacity
                  onPress={toggleShareModal}
                  style={{
                    alignItems: "center",
                    marginRight: index === 3 ? 0 : 25,
                  }}
                >
                  {item.logo}
                  <Text style={styles.shareModalText}>{item.label}</Text>
                </TouchableOpacity>
              );
            }}
          />
          {/* DIVIVER */}
          <View style={{ height: 1, backgroundColor: "#C8C8C8", marginTop: 26 }} />
          {/* CANCEL */}
          <TextButton
            label={"Cancel"}
            labelStyle={{ color: "#A7A7A7" }}
            containerStyle={{ marginVertical: 20 }}
            onPress={toggleShareModal}
          />
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* <StatusBar backgroundColor={COLORS.red} /> */}
      {/* HEADER */}
      <View style={{ flex: 0.1, backgroundColor: COLORS.red }} />

      <View style={styles.innerContainer}>
        {/* PROFILE_IMAGE */}
        <Image source={user.photo ? { uri: user.photo } : person} style={styles.profileContainer} />
        {/* SETTINGS_BUTTON */}
        <ButtonwithIcon
          icon={<Settings1 width={18} height={18} fill={COLORS.black} />}
          containerStyle={styles.settingsIconStyles}
          onPress={() => navigation.navigate("Settings")}
        />
        <View style={{ height: 58, backgroundColor: COLORS.white }} />
        {/* PROFILE_ACTONS_LIST */}
        <ScrollView contentContainerStyle={{}} showsVerticalScrollIndicator={false}>
          {/* NAME */}
          <TextButtonwithIcon
            label={user.name}
            rightIcon={<CrowCircle1 />}
            labelStyle={{
              fontSize: 16,
              marginRight: 7,
              fontFamily: FONTS.Nunito_SemiBold,
            }}
            containerStyle={{ alignSelf: "center" }}
            disabled
          />
          <View style={{ marginTop: 27, marginHorizontal: 20 }}>
            {/* <ProfileAction
              LeftIcon={<CrownCircle />}
              RightIcon={<ArrowRight fill={COLORS.white} />}
              label="Manage Subscription"
              iconBackgroundColor={COLORS.red}
              containerStyle={{height: 60, backgroundColor: COLORS.red}}
              onPress={() => navigation.navigate('Subscription')}
            /> */}
            {/* MY_PROGRAM */}
            <ProfileAction
              LeftIcon={<Book />}
              RightIcon={<ArrowRight fill={COLORS.black} />}
              label="My Programs"
              iconBackgroundColor={COLORS.red}
              containerStyle={{ marginTop: 16 }}
              onPress={() => navigation.navigate("MyProgram")}
            />
            {/* CALENDAR */}
            <ProfileAction
              LeftIcon={<Calendar1 />}
              RightIcon={<ArrowRight fill={COLORS.black} />}
              label="Calendar"
              containerStyle={{ marginTop: 7 }}
              iconBackgroundColor={COLORS.red}
              onPress={() => navigation.navigate("PatientAgendaView")}
            />
            {/*TITLE */}
            <Text style={styles.title}>Support</Text>
            <ProfileAction LeftIcon={<About />} label="About Us" onPress={() => navigation.navigate("AboutUs")} />
            <ProfileAction
              LeftIcon={<Image source={Community} style={{ width: 12, height: 12 }} />}
              label="Community"
              containerStyle={{ marginTop: 7 }}
              onPress={() => navigation.navigate("CommunityList")}
            />
            <ProfileAction
              LeftIcon={<Support />}
              label="Contact Support"
              containerStyle={{ marginTop: 7 }}
              onPress={() => navigation.navigate("ContactSupport")}
            />
            <ProfileAction
              LeftIcon={<Privacy />}
              label="Privacy Policy"
              containerStyle={{ marginTop: 7 }}
              onPress={() => navigation.navigate("PrivacyPolicy")}
            />
            <ProfileAction
              LeftIcon={<Privacy />}
              label="Terms & Conditions"
              containerStyle={{ marginTop: 7 }}
              onPress={() => navigation.navigate("TermsAndConditions")}
            />
            {/*TITLE */}
            <Text style={styles.title}>Other</Text>
            <ProfileAction
              LeftIcon={<Logout />}
              label="Sign Out"
              containerStyle={{ marginTop: 7, marginBottom: 40 }}
              onPress={toggleLogoutModal}
            />
          </View>
        </ScrollView>
        {renderShareModal()}
        {/* {renderLogoutModal()} */}
        <ConfirmModal
          isVisible={isLogoutModalVisible}
          onClose={toggleLogoutModal}
          onConfirm={() => {
            dispatch(setLogoutAction());
            dispatch(bookingResetAction());
            toggleLogoutModal();
          }}
          title="Log Out?"
          message="Are you sure want to logout?"
          confirmLabel="Yes! Log me out"
        />
      </View>
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.red,
  },
  innerContainer: {
    flex: 0.9,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 35,
    borderTopRightRadius: 35,
  },
  profileContainer: {
    width: 100,
    height: 100,
    alignSelf: "center",
    position: "absolute",
    top: -50,
    backgroundColor: "white",
    borderRadius: 100,
    zIndex: 20,
  },
  settingsIconStyles: {
    // marginTop: 32,
    // alignSelf: 'flex-end',
    // marginRight: 28,
    // backgroundColor: 'red',
    position: "absolute",
    right: 32,
    top: 32,
    zIndex: 10,
  },
  title: {
    fontSize: 16,
    color: "#292D32",
    marginTop: 20,
    marginBottom: 6,
    fontFamily: FONTS.Nunito_Bold,
  },
  modalContainer: {
    padding: 30,
    backgroundColor: COLORS.white,
  },

  shareModalText: {
    marginTop: 13,
    textAlign: "center",
    fontFamily: FONTS.Nunito_Regular,
  },
});
