import { <PERSON>B<PERSON>ground, SafeAreaView, <PERSON>roll<PERSON>iew, StyleSheet, Text, View } from "react-native";
import React, { useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import {
  AppointmentSchdule,
  ButtonwithIcon,
  ChooseAppointment,
  TextButton,
  TextButtonwithIcon,
} from "../../../components";
import { ArrowLeft1, Location, Star1 } from "../../../assets/svgicons";
import Modal from "react-native-modal";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { useFormik } from "formik";
import { EditBookingScreenParams } from "../../../models/IBooking";
import { useAppSelector } from "../../../redux/config/Store";
import { IOfferService } from "../../../models/IOfferService";
import FastImage from "react-native-fast-image";
type Props = NativeStackScreenProps<HomeStackParamList, "DoctorDetails">;

const DoctorDetails: React.FC<Props> = ({ navigation, route }) => {
  const { doctor } = route.params;

  const [isModalVisible, setModalVisible] = React.useState(false);
  const [isScheduleModalVisible, setIsScheduleModalVisible] = React.useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<IOfferService>();
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleModalScedule = () => {
    setIsScheduleModalVisible(!isScheduleModalVisible);
  };

  const formik = useFormik({
    initialValues: {
      service: {},
      bookingDateTime: {},
    },
    onSubmit: (formValues) => {
      navigation.navigate("EditBookingDetails", {
        bookingData: {
          bookingDateTime: formik.values.bookingDateTime,
          service: formik.values.service,
          doctor,
          selectedAppointment: selectedAppointment,
        } as EditBookingScreenParams,
      });
    },
  });

  const empty_avatar =
    "https://img.freepik.com/premium-vector/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-vector-illustration_561158-3383.jpg?semt=ais_hybrid";

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 12, flexGrow: 1, justifyContent: "space-between" }}
      >
        <View>
          <FastImage resizeMode="cover" source={{ uri: doctor?.photo || empty_avatar }} style={styles.imageContainer}>
            {/* BACK_ARROW_BUTTON */}
            <ButtonwithIcon
              icon={<ArrowLeft1 fill={COLORS.white} width={24} height={24} />}
              containerStyle={styles.backArrowContainer}
              onPress={() => navigation.goBack()}
            />
          </FastImage>
          <View style={{ marginTop: 18, paddingHorizontal: 20 }}>
            {/* NAME */}
            <Text style={styles.name}>{doctor?.name}</Text>
            {/* ADDRES */}
            {doctor?.address ? (
              <TextButtonwithIcon
                leftIcon={<Location />}
                label={doctor?.address}
                labelStyle={styles.address}
                containerStyle={{ marginTop: 6 }}
                disabled
              />
            ) : null}
            {/* RATING_AND_INFO_CONTAINER */}
            <View style={{ marginTop: 12, flexDirection: "row" }}>
              <View style={styles.info}>
                <Text style={styles.infoText}>Therapist</Text>
              </View>
              {/* RATING */}
              {doctor?.avgRating ? (
                <TextButtonwithIcon
                  label={doctor?.avgRating?.toString()}
                  labelStyle={styles.ratingText}
                  rightIcon={<Star1 />}
                  containerStyle={styles.ratingContainer}
                  disabled
                />
              ) : null}
            </View>
            {/* ABOUT_DOCTOR */}
            <Text style={styles.subTitle}>About Doctor</Text>
            {/* DISCRIPTION */}
            <Text style={styles.paragraph}>{doctor?.introduction}</Text>
          </View>
        </View>
        <TextButton label={"Book Appointment"} containerStyle={styles.appointmentButton} onPress={toggleModal} />
      </ScrollView>
      <Modal
        isVisible={isModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModal()}
        swipeDirection={"down"}
        onSwipeComplete={() => setModalVisible(false)}
        style={styles.appointmentModalContainer}
      >
        <ChooseAppointment
          services={doctor.services}
          onContinuePress={(v) => {
            formik.setFieldValue("service", v);
            toggleModal();
            setTimeout(() => {
              toggleModalScedule();
            }, 500);
          }}
          setSelectedAppointment={setSelectedAppointment}
        />
      </Modal>
      <Modal
        isVisible={isScheduleModalVisible}
        backdropOpacity={0.3}
        onBackdropPress={() => toggleModalScedule()}
        style={styles.scheduleModalContainer}
        swipeDirection={"down"}
        animationOutTiming={500}
        onSwipeComplete={() => setIsScheduleModalVisible(false)}
      >
        <View>
          <AppointmentSchdule
            schedule={doctor.schedule}
            doctorId={doctor._id}
            onPress={(v) => {
              formik.setFieldValue("bookingDateTime", v);
              toggleModalScedule();
              formik.handleSubmit();
            }}
            selectedAppointment={selectedAppointment}
          />
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default DoctorDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: sizes.paddingTop,
  },
  imageContainer: {
    height: 280,
    width: "100%",
    alignItems: "flex-start",
  },
  backArrowContainer: {
    margin: 22,
    backgroundColor: "#00000040",
    width: 32,
    height: 32,
    borderRadius: 4,
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  address: {
    fontSize: 12,
    color: "#C7C7C7",
    marginLeft: 5,
    fontFamily: FONTS.Nunito_Light,
  },
  info: {
    width: 90,
    height: 30,
    backgroundColor: "#EB4E1F20",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 30 / 2,
  },
  infoText: {
    fontSize: 10,
    color: COLORS.red,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  ratingText: {
    fontSize: 14,
    color: "#FCBD18",
    marginRight: 6,
    fontFamily: FONTS.Nunito_Bold,
  },
  ratingContainer: {
    width: 60,
    height: 30,
    backgroundColor: "#FCBD1830",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 60 / 2,
    marginLeft: 11,
  },
  subTitle: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 21,
    fontFamily: FONTS.Nunito_SemiBold,
  },
  paragraph: {
    fontSize: 12,
    color: "#ABABAB",
    marginTop: 11,
    fontFamily: FONTS.Nunito_Regular,
  },
  footer: {},
  appointmentButton: {
    height: 50,
    borderRadius: 8,
    backgroundColor: COLORS.red,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
    marginHorizontal: 16,
    marginBottom: 40,
    marginTop: 12,
  },
  appointmentModalContainer: {
    margin: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: "flex-end",
  },
  scheduleModalContainer: {
    margin: 0,
    justifyContent: "flex-end",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});
