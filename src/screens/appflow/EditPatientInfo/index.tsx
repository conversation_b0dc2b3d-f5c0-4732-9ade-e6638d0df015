import React, {FC, useEffect, useState} from 'react';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {StatusBar, StyleSheet, Text, View} from 'react-native';
import {COLORS, FONTS} from '../../../themes';
import {Forminput, GooglePlaces, TextButton} from '../../../components';
import {User} from '../../../assets/svgicons';
import * as Yup from 'yup';
import {useFormik} from 'formik';
import AuthServices from '../../../Services/AuthServices';
import {useAppSelector, useAppDispatch} from '../../../redux/config/Store';
import {AxiosError} from 'axios';
import {updateUser} from '../../../redux/slices/AuthSlice';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

// Define Yup validation schema
const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  address: Yup.string().required('Address is required'),
});

interface UserInfo {
  name: string;
  address: string;
}

type Props = NativeStackScreenProps<HomeStackParamList, 'EditPatientInfo'>;

const EditPatientInfo: FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {user} = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();

  // Formik setup
  const formik = useFormik<UserInfo>({
    initialValues: {
      name: '',
      address: '',
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: async ({name, address}) => {
      setIsLoading(true);
      await AuthServices.UpdateUser(user._id, {data: {name, address}})
        .then(res => {
          navigation.goBack();
          dispatch(updateUser({name, address}));
        })
        .catch((error: AxiosError) => {})
        .finally(() => {
          setIsLoading(false);
        });
    },
  });

  useEffect(() => {
    if (user) {
      formik.setFieldValue('name', user.name);
      formik.setFieldValue('address', user?.address);
    }
  }, []);

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{flexGrow: 1}}>
        <StatusBar backgroundColor={COLORS.white} />
        <Text style={styles.title}>Update</Text>
        <Text style={styles.paragraph}>Manage your name and location</Text>
        <Text style={[styles.inputLabel, {marginTop: 20}]}>Full Name</Text>
        <Forminput
          placeholder={'Your Full Name'}
          placeholderTextColor="#292D3260"
          leftIcon={<User />}
          containerStyle={{marginTop: 6}}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          value={formik.values.name}
          errorMessage={formik.touched.name && formik.errors.name}
        />

        <Text style={[styles.inputLabel, {marginTop: 16, marginBottom: 10}]}>
          Address
        </Text>
        <GooglePlaces
          onPressAddress={address => formik.setFieldValue('address', address)}
        />

        <TextButton
          label={'Save'}
          containerStyle={styles.nextButton}
          onPress={formik.handleSubmit}
          isLoading={isLoading}
          disabled={isLoading || !formik.isValid}
        />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default EditPatientInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 24,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Regular,
  },
  inputLabel: {
    color: COLORS.black,
    fontSize: 14,
    fontFamily: FONTS.Nunito_Medium,
  },
  nextButton: {
    height: 55,
    borderRadius: 8,
    backgroundColor: COLORS.red,
    marginVertical: 16,
  },
});
