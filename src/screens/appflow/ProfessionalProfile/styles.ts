import {StyleSheet} from 'react-native';
import {COLORS, FONTS} from '../../../themes';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  profileContainer: {
    backgroundColor: COLORS.red,
    alignItems: 'center',
    paddingTop: 14,
    paddingBottom: 50,
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  imageContainer: {
    width: 80,
    height: 80,
    borderRadius: 6,
    overflow: 'hidden',
    paddingLeft: 20,
    backgroundColor: 'white',
  },
  innerProfileContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingLeft: 16,
  },
  name: {
    fontSize: 16,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Bold,
  },
  speciality: {
    fontSize: 7,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Medium,
  },
  ratingContainer: {
    width: 20,
    height: 20,
    borderRadius: 5,
    backgroundColor: COLORS.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileAction: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  modalContainer: {
    padding: 30,
    backgroundColor: COLORS.white,
  },
  logoutModalContainer: {
    paddingTop: 30,
    paddingHorizontal: 15,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  modalTitle: {
    fontSize: 18,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
  logoutButton: {
    backgroundColor: COLORS.red,
    marginTop: 36,
    height: 42,
    borderRadius: 10,
  },
});
