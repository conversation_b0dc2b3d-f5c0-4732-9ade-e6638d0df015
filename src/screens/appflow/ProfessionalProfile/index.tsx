import { <PERSON>, ScrollView, <PERSON>B<PERSON>, Text, View } from "react-native";
import React from "react";
import { ButtonwithIcon, ProfileAction, TextButton, TextButtonwithIcon } from "../../../components";
import { COLORS, FONTS } from "../../../themes";
import { Community, Contact, Dollar, Profile3, Term, About1, person } from "../../../assets/images";
import { Calendar1, Location1, NotoStar, Privacy, Settings1, User1, Logout } from "../../../assets/svgicons";
import Modal from "react-native-modal";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalTabParamList } from "../../../navigation/ProfessionalBottomTab";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { setLogoutAction } from "../../../redux/slices/AuthSlice";

import { styles } from "./styles";
import { bookingResetAction } from "../../../redux/slices/BookingsSlice";
type Props = NativeStackScreenProps<ProfessionalTabParamList, "ProfessionalProfile">;
const ProfessionalProfile: React.FC<Props> = ({ navigation }) => {
  // const {setIsProfessional} = useUser();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  const [isLogoutModalVisible, setLogoutModalVisible] = React.useState(false);

  const toggleLogoutModal = () => {
    setLogoutModalVisible(!isLogoutModalVisible);
  };
  const renderLogoutModal = () => {
    return (
      <Modal
        isVisible={isLogoutModalVisible}
        onBackdropPress={() => toggleLogoutModal()}
        backdropOpacity={0.2}
        style={{ margin: 0, position: "absolute", bottom: 0, left: 0, right: 0 }}
      >
        <View style={styles.logoutModalContainer}>
          {/* TITLE */}
          <Text style={styles.modalTitle}>Log Out?</Text>
          {/* PARAGRAPH */}
          <Text style={styles.paragraph}>Are you sure want to logout?</Text>
          {/* LOGOUT_BUTTON */}
          <TextButton
            label={"Yes! Log me out"}
            labelStyle={{ color: COLORS.white }}
            containerStyle={styles.logoutButton}
            onPress={() => {
              dispatch(setLogoutAction());
              dispatch(bookingResetAction());
              toggleLogoutModal();
            }}
          />
          {/* CANCEL */}
          <TextButton
            label={"Cancel"}
            labelStyle={{ color: "#A7A7A7" }}
            containerStyle={{ marginVertical: 20 }}
            onPress={toggleLogoutModal}
          />
        </View>
      </Modal>
    );
  };
  return (
    <View style={styles.container}>
      {/* <StatusBar backgroundColor={COLORS.red} /> */}
      <View style={styles.profileContainer}>
        <Image source={user.photo ? { uri: user.photo } : person} style={styles.imageContainer} />
        <View style={styles.innerProfileContainer}>
          <View>
            <Text style={styles.name}>{user.name}</Text>
            <Text style={styles.speciality}>{user.userType}</Text>
            {user?.averageRating ? (
              <TextButtonwithIcon
                leftIcon={<NotoStar width={16} height={16} />}
                label={"4.5"}
                labelStyle={{ fontSize: 14, color: COLORS.white, marginLeft: 5 }}
                disabled
                leftIconContainer={styles.ratingContainer}
                containerStyle={{ paddingTop: 6 }}
              />
            ) : null}
          </View>
          <ButtonwithIcon icon={<Settings1 fill={COLORS.white} />} onPress={() => navigation.navigate("Settings")} />
        </View>
      </View>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.profileAction}>
        <ProfileAction
          LeftIcon={<Calendar1 />}
          label="Connected Account"
          containerStyle={{ marginTop: 7 }}
          iconBackgroundColor={COLORS.red}
          onPress={() => navigation.navigate("ConnectedAccount")}
        />
        <ProfileAction
          LeftIcon={<Image source={Community} style={{ width: 12, height: 12 }} />}
          label="Community"
          containerStyle={{ marginTop: 7 }}
          onPress={() => navigation.navigate("CommunityList")}
          iconBackgroundColor={COLORS.red}
        />
        <ProfileAction
          LeftIcon={<Calendar1 />}
          label="Calendar"
          containerStyle={{ marginTop: 7 }}
          iconBackgroundColor={COLORS.red}
          onPress={() => navigation.navigate("ProfessionalAgendaView")}
        />
        {/* <ProfileAction
          LeftIcon={<User1 />}
          label="Manage Profile"
          containerStyle={{marginTop: 7}}
          // onPress={() => navigation.navigate('Community')}
        />
        <ProfileAction
          LeftIcon={<Location1 />}
          label="Location"
          containerStyle={{marginTop: 7}}
        />
        <ProfileAction
          LeftIcon={<Image source={Dollar} style={{width: 14, height: 14}} />}
          label="Accounts"
          containerStyle={{marginTop: 7}}
          onPress={() => navigation.navigate('EarningsDetails')}
        /> */}
        <ProfileAction
          LeftIcon={<Image source={Contact} style={{ width: 12, height: 12 }} />}
          label="Contact Support"
          containerStyle={{ marginTop: 7 }}
          onPress={() => navigation.navigate("ContactSupport")}
        />
        <ProfileAction
          LeftIcon={<Privacy />}
          label="Privacy Policy"
          containerStyle={{ marginTop: 7 }}
          onPress={() => navigation.navigate("PrivacyPolicy")}
        />
        <ProfileAction
          LeftIcon={<Image source={Term} style={{ width: 12, height: 12 }} />}
          label="Terms & Conditions"
          containerStyle={{ marginTop: 7 }}
          onPress={() => navigation.navigate("TermsAndConditions")}
        />
        <ProfileAction
          LeftIcon={<Image source={About1} style={{ width: 12, height: 12 }} />}
          label="About Us"
          onPress={() => navigation.navigate("AboutUs")}
          containerStyle={{ marginTop: 7 }}
        />
        <ProfileAction
          LeftIcon={<Logout />}
          label="Log Out"
          containerStyle={{ marginTop: 7, marginBottom: 40 }}
          onPress={toggleLogoutModal}
        />
        {renderLogoutModal()}
      </ScrollView>
    </View>
  );
};

export default ProfessionalProfile;
