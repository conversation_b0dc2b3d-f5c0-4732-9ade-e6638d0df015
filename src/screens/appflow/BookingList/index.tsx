import { StatusBar, StyleSheet, View, ScrollView, ActivityIndicator, Text, RefreshControl } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS } from "../../../themes";
import { PatientBookingCard, BookingTabs, ButtonwithIcon, EmptyState } from "../../../components";
import { Calendar } from "../../../assets/images";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { useAppSelector, useAppDispatch } from "../../../redux/config/Store";
import {
  fetchActiveBookingsThunk,
  fetchBookings,
  fetchCompletedBookingsThunk,
} from "../../../redux/slices/BookingsSlice";
import { Calendar4 } from "../../../assets/svgicons";
import { useFocusEffect } from "@react-navigation/native";
type Props = NativeStackScreenProps<HomeStackParamList, "BookingList">;

const BookingList: React.FC<Props> = ({ navigation }) => {
  const [selectedTab, setSelectedTab] = useState<"Active" | "Past">("Active");
  const [refreshing, setRefreshing] = useState(false);

  const dispatch = useAppDispatch();
  const { results: active_bookings, loading } = useAppSelector((state) => state.bookings.active);
  const { results: completed_bookings } = useAppSelector((state) => state.bookings.completed);
  const { userId } = useAppSelector((state) => state.auth);

  const results = selectedTab === "Active" ? active_bookings : completed_bookings;

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <ButtonwithIcon icon={<Calendar4 />} onPress={() => navigation.navigate("PatientAgendaView")} />
      ),
    });
  }, [navigation]);

  const onPullToRefresh = () => {
    dispatch(fetchActiveBookingsThunk({ userId, userType: "patient" }));
    dispatch(fetchCompletedBookingsThunk({ userId, userType: "patient" }));
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} />
      {/* HEADER */}
      <BookingTabs activeTab={selectedTab} setActiveTab={setSelectedTab} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={{ color: "#000" }}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : null}
      {!loading ? (
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onPullToRefresh}
              colors={["#ff0000", "#00ff00", "#0000ff"]} // Android: Red, Green, Blue
              tintColor="#ff0000" // iOS: Red
            />
          }
        >
          <View style={styles.innerContainer}>
            {results.length ? (
              results.map((appointment) => (
                <PatientBookingCard
                  key={appointment._id}
                  startSession={selectedTab === "Active"}
                  booking={appointment}
                  onPress={() =>
                    navigation.navigate("BookingDetails", {
                      bookingId: appointment._id,
                    })
                  }
                  onStartSessionPress={() =>
                    navigation.navigate("Session", {
                      sessionId: appointment._id,
                      doctorId: appointment.doctor._id,
                    })
                  }
                />
              ))
            ) : (
              <EmptyState
                image={Calendar}
                title={"You have no appointments scheduled"}
                paragraph={
                  "Book on appointment with a therapist to see this page filled with content and your heart filled with delight."
                }
              />
            )}
          </View>
        </ScrollView>
      ) : null}
    </View>
  );
};

export default BookingList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 20,
  },
  innerContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 16,
  },
  activeBookings: {
    marginTop: 23,
    paddingVertical: 19,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 10,
  },
});
