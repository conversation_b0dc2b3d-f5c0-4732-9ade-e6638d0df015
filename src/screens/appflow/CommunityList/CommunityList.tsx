import {
  ActivityIndicator,
  SectionList,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {FC, useEffect} from 'react';
import {appStyles, COLORS, FONTS} from '../../../themes';
import {CommunityCard} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import useCommunity from '../../../Hooks/useCommunity';
import {useAppSelector} from '../../../redux/config/Store';
type Props = NativeStackScreenProps<HomeStackParamList, 'CommunityList'>;

const CommunityList: FC<Props> = ({navigation}) => {
  const userId = useAppSelector(state => state.auth.userId);
  const {communities, loadingCommunities, getCommunities, joinCommunity} =
    useCommunity();

  useEffect(() => {
    const unsubscribe = getCommunities(); // Fetch data when screen mounts
    return () => unsubscribe(); // Cleanup on unmount
  }, []);

  // Ensure communities is an array to prevent undefined errors
  const joinedCommunities = (communities || []).filter(item =>
    item.currentMembers?.includes(userId),
  );

  const unjoinedCommunities = (communities || []).filter(
    item => !item.currentMembers?.includes(userId),
  );

  // Prepare sections array (ensure it's never undefined)
  const sections = [];
  if (joinedCommunities.length > 0) {
    sections.push({title: 'Your Board', data: joinedCommunities});
  }
  if (unjoinedCommunities.length > 0) {
    sections.push({title: 'Select Topic', data: unjoinedCommunities});
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      {/* HEADER */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Welcome to OW community</Text>
        <Text style={styles.headerParagraph}>Lorem ipsum dor sit met ato</Text>
      </View>

      {/* COMMUNITY LIST */}
      {loadingCommunities ? (
        <View style={appStyles.loadingContainer}>
          <Text style={appStyles.loadingParagraph}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <SectionList
          sections={sections}
          keyExtractor={(item, index) => item.id + index}
          renderItem={({item}) => {
            const isUserJoined = item.currentMembers?.includes(userId);
            return (
              <CommunityCard
                title={item?.topicName}
                subTitle={`${item?.currentMembers?.length} people are participating`}
                containerStyle={{marginTop: 14}}
                onPress={() =>
                  navigation.navigate('CommunityChatDetails', {
                    communityId: item.id,
                    communityLenght: item?.currentMembers?.length,
                    topicName: item.topicName,
                  })
                }
                onPressJoin={() => joinCommunity(item.id, userId)}
                isUserJoined={isUserJoined}
                disableCard={!isUserJoined}
              />
            );
          }}
          renderSectionHeader={({section: {title}}) => (
            <View style={styles.sectionHeader}>
              <Text style={styles.title}>{title}</Text>
            </View>
          )}
          contentContainerStyle={{paddingHorizontal: 20, marginTop: 24}}
        />
      )}
    </View>
  );
};

export default CommunityList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    backgroundColor: COLORS.red,
    height: 80,
    borderTopRightRadius: 80 / 2,
    borderBottomRightRadius: 80 / 2,
    marginTop: 15,
    justifyContent: 'center',
    paddingLeft: 20,
  },
  headerTitle: {
    fontSize: 20,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_ExtraBold,
  },
  headerParagraph: {
    fontSize: 12,
    color: COLORS.white,
    fontFamily: FONTS.Nunito_Regular,
  },
  sectionHeader: {
    marginTop: 20,
  },
  title: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
});
