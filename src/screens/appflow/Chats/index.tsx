import { ActivityIndicator, SafeAreaView, StyleSheet, Text, View } from "react-native";
import React, { FC, useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import { FlatList } from "react-native";
import { AllContacts } from "../../../constants";
import { ChatCard } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { IUser } from "../../../models";

type Props = NativeStackScreenProps<HomeStackParamList, "Chats">;
import useConvetsation from "../../../Hooks/useConvetsation";

const Chats: FC<Props> = ({ navigation }) => {
  const { getConversations, conversations, conversationsLoading } = useConvetsation();
  useEffect(() => {
    getConversations();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      {/* <Text style={styles.title}>Chat</Text> */}

      {conversationsLoading ? (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Text>Loading...</Text>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={conversations}
          contentContainerStyle={{ marginTop: 36 }}
          ListEmptyComponent={<Text style={styles.heading}>No Chats Found</Text>}
          renderItem={({ item }) => {
            return (
              <ChatCard
                item={item}
                onPress={(recipientData: IUser) => {
                  navigation.navigate("ChatDetails", {
                    otherUser: recipientData,
                  });
                }}
              />
            );
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default Chats;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderColor: COLORS.white,
    padding: 16,
    backgroundColor: COLORS.white,
  },
  title: {
    fontSize: 18,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    paddingLeft: 24,
  },
  heading: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    textAlign: "center",
    paddingTop: 54,
  },
});
