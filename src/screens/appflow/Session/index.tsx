import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { AppButton, Rating } from "../../../components";
import { Cross2 } from "../../../assets/svgicons";
import { COLORS, FONTS } from "../../../themes";
import Modal from "react-native-modal";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
type Props = NativeStackScreenProps<HomeStackParamList, "Session">;
import { ZegoUIKitPrebuiltCall, ONE_ON_ONE_VIDEO_CALL_CONFIG } from "@zegocloud/zego-uikit-prebuilt-call-rn";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import {
  fetchActiveBookingsThunk,
  fetchCompletedBookingsThunk,
  markBookingComplete,
  updateBookingAction,
} from "../../../redux/slices/BookingsSlice";
import RatingServices from "../../../Services/RatingServices";
import firestore from "@react-native-firebase/firestore";
import { fetchBookings } from "../../../redux/slices/BookingsSlice";
import BookingService from "../../../Services/BookingService";

interface IRating {
  ratingCount: number;
  ratingText: string;
}
const Session: React.FC<Props> = ({ navigation, route }) => {
  const { sessionId, doctorId } = route.params;

  const userId = useAppSelector((state) => state.auth.userId);
  const user = useAppSelector((state) => state.auth.user);

  const [isReviewVisible, setReviewVisible] = React.useState(false);
  const [rating, setRating] = useState<IRating>({
    ratingCount: 0,
    ratingText: "",
  });
  const [ratingLoading, setRatingLoading] = useState(false);
  const [isDisconnetedCall, setIsDisconnetedCall] = useState(false);

  const dispatch = useAppDispatch();

  const onCloseReviewModal = async () => {
    setRatingLoading(true);
    if (user.userType === "doctor") {
      await dispatch(markBookingComplete(sessionId)).unwrap();
    } else {
      await BookingService.UpdateBooking(sessionId, { status: "completed" });
    }
    setReviewVisible(false);
    setRatingLoading(false);
    setTimeout(() => {
      navigation.goBack();
      dispatch(fetchActiveBookingsThunk({ userId, userType: user.userType }));
      dispatch(fetchCompletedBookingsThunk({ userId, userType: user.userType }));
    }, 100);
  };

  // ADD REVIEW
  const addReviewonBooking = async () => {
    setRatingLoading(true);

    // sessionId is a booking id
    const payload = {
      rating: rating.ratingCount,
      review: rating.ratingText,
      reviewBy: userId,
      bookingId: sessionId,
    };
    await RatingServices.AddRating(doctorId, payload)

      .then((respose) => {
        dispatch(
          updateBookingAction({
            _id: sessionId,
            status: "completed",
            review: { rating: payload.rating, review: payload.review },
          }),
        );
      })
      .catch((error) => {
        console.log("error---------", error);
      })
      .finally(() => {
        onCloseReviewModal();
        setRatingLoading(false);
      });
  };

  const sessionRef = firestore().collection("Sessions").doc(sessionId);

  // Function to handle when a user joins the session
  const handleUserJoinSession = async () => {
    try {
      const sessionDoc = await sessionRef.get();

      if (sessionDoc.exists) {
        const sessionData = sessionDoc.data();
        if (!sessionData.joinedUsers.includes(userId)) {
          // Add the user to joinedUsers array
          await sessionRef.update({
            joinedUsers: firestore.FieldValue.arrayUnion(userId),
            updatedAt: firestore.FieldValue.serverTimestamp(),
          });
        }
      } else {
        // Create a new session document if it doesn't exist
        await sessionRef.set({
          joinedUsers: [userId],
          isCompleted: false,
        });
      }

      console.log("User join recorded in Firestore");
    } catch (error) {
      console.error("Error updating session:", error);
    }
  };

  // Function to handle call end
  const handleCallEnd = async (callID, reason, duration) => {
    try {
      const sessionDoc = await sessionRef.get();

      if (sessionDoc.exists) {
        setIsDisconnetedCall(true);
        const sessionData = sessionDoc.data();
        if (sessionData.joinedUsers.length >= 2) {
          setReviewVisible(true);
          // if (user.userType === 'doctor') {
          // Mark session as completed
          await sessionRef.update({ isCompleted: true });
        } else {
          navigation.goBack();
          await sessionRef.update({ isCompleted: false, joinedUsers: [] });
          console.log("Both users have not joined yet.");
        }
      }
    } catch (error) {
      console.error("Error completing session:", error);
    }
  };

  // Run when component mounts
  useEffect(() => {
    console.log("sessioId >>", sessionId);
    handleUserJoinSession();
  }, []);

  const renderModal = () => {
    return (
      <View>
        <Modal isVisible={isReviewVisible} backdropOpacity={0.3} onBackdropPress={() => onCloseReviewModal()}>
          <View style={styles.modalContainer}>
            <TouchableOpacity hitSlop={10} style={styles.crossButtonContainer} onPress={() => onCloseReviewModal()}>
              <Cross2 width={18} height={18} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Your session has been completed!</Text>
            {user.userType === "doctor" ? (
              <AppButton
                title="Submit"
                isLoading={ratingLoading}
                onPress={() => onCloseReviewModal()}
                containerStyle={styles.button}
                disabled={ratingLoading}
              />
            ) : (
              <Rating
                setRating={setRating}
                rating={rating}
                onPressRatingButton={() => addReviewonBooking()}
                ratingLoading={ratingLoading}
                title="Please rate your experience"
                containerStyle={{ padding: 0 }}
                ratingContainer={{ alignItems: "center", marginVertical: 18 }}
                titleStyle={styles.paragraph}
              />
            )}
          </View>
        </Modal>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {isDisconnetedCall ? (
        <View style={styles.emptyView} />
      ) : (
        <ZegoUIKitPrebuiltCall
          appID={1072161791}
          appSign={"1b1e89791af88dcd1ec4a5285ba11de23e8b361feb40d95936dd3083873578fa"}
          userID={user._id} // userID can be something like a phone number or the user id on your own user system.
          userName={user.name}
          callID={sessionId} // callID can be any unique string.
          config={{
            // You can also use ONE_ON_ONE_VOICE_CALL_CONFIG/GROUP_VIDEO_CALL_CONFIG/GROUP_VOICE_CALL_CONFIG to make more types of calls.
            ...ONE_ON_ONE_VIDEO_CALL_CONFIG,
            onCallEnd: handleCallEnd,
          }}
        />
      )}

      {renderModal()}
    </View>
  );
};

export default Session;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  modalContainer: {
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 12,
    // alignItems: 'center',
  },
  modalTitle: {
    fontSize: 16,
    color: COLORS.black,
    textAlign: "center",
    fontFamily: FONTS.Nunito_Bold,
  },
  paragraph: {
    fontSize: 11,
    color: "#263238",
    textAlign: "center",
    fontFamily: FONTS.Nunito_Regular,
  },
  crossButtonContainer: {
    alignSelf: "flex-end",
    marginBottom: 16,
  },
  emptyView: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  button: {
    marginHorizontal: 0,
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    height: 48,
  },
});
