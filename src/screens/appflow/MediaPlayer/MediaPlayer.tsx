import { ActivityIndicator, Platform, StatusBar, StyleSheet, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import Video from "react-native-video";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { COLORS } from "../../../themes";
import { Cross } from "../../../assets/svgicons";
import { firebase } from "@react-native-firebase/firestore";
import { useAppSelector } from "../../../redux/config/Store";
import { IProgram, IUserProgram } from "../../../models/IProgram";

type Props = NativeStackScreenProps<HomeStackParamList, "MediaPlayer">;

const MediaPlayer: React.FC<Props> = ({ navigation, route }) => {
  const { programId, videoUrl, exerciseId, isAssigned } = route.params;
  const userId = useAppSelector((state) => state.auth.userId);
  const [loading, setLoading] = useState<boolean>(true);
  const [programData, setProgramData] = useState<IUserProgram | IProgram | null>(null);
  const [assignedProgramDocId, setAssignedProgramDocId] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserProgram = async () => {
      try {
        const querySnapshot = await firebase
          .firestore()
          .collection("UserPrograms")
          .where("userId", "==", userId)
          .where("programId", "==", programId)
          .limit(1)
          .get();

        if (!querySnapshot.empty) {
          const doc = querySnapshot.docs[0];
          const program = { id: doc.id, ...doc.data() } as IUserProgram;
          setProgramData(program);
          console.log("No matching program found.");
        } else {
          console.log("No matching program found.");
        }
      } catch (error) {
        console.error("Error fetching user program:", error);
      }
    };
    const fetchUserAssignedProgram = async () => {
      try {
        const querySnapshot = await firebase
          .firestore()
          .collection("AssignedPrograms")
          .where("patientId", "==", userId)
          .where("programId", "==", programId)
          .limit(1)
          .get();

        if (!querySnapshot.empty) {
          const doc = querySnapshot.docs[0];
          const program = { id: doc.id, ...doc.data().programData } as IProgram;
          setProgramData(program);
          setAssignedProgramDocId(doc.id);
          console.log("Assigned program found:", program);
        } else {
          console.log("No matching assigned program found.");
        }
      } catch (error) {
        console.error("Error fetching assigned program:", error);
      }
    };
    programId ? (isAssigned ? fetchUserAssignedProgram() : fetchUserProgram()) : null;
  }, [userId, programId, isAssigned]);

  const updateUserProgram = async () => {
    if (!programData) {
      return;
    }

    try {
      if (isAssigned && assignedProgramDocId) {
        // Update AssignedPrograms collection for assigned programs
        await firebase
          .firestore()
          .collection("AssignedPrograms")
          .doc(assignedProgramDocId)
          .update({
            "programData.completedExercises": firebase.firestore.FieldValue.arrayUnion(exerciseId),
          });

        console.log("Exercise added to assigned program successfully");
      } else {
        // Update UserPrograms collection for regular programs
        await firebase
          .firestore()
          .collection("UserPrograms")
          .doc(programData.id)
          .update({
            completedExercises: firebase.firestore.FieldValue.arrayUnion(exerciseId),
          });

        console.log("Exercise added to user program successfully");
      }
    } catch (error) {
      console.error("Error updating completed exercises:", error);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.black} />
      {loading && <ActivityIndicator size="large" color={COLORS.white} style={styles.loadingIndicator} />}

      {/* Video Player */}
      <Video
        source={{ uri: videoUrl }}
        onLoadStart={() => setLoading(true)}
        onLoad={() => setLoading(false)}
        onError={(error) => {
          console.error("Error in video playback", error);
          setLoading(false);
        }}
        controls={true}
        muted={false}
        style={styles.videoContainer}
        resizeMode="contain"
        // repeat
        onEnd={updateUserProgram}
      />

      <TouchableOpacity
        hitSlop={{ left: 20, right: 20, bottom: 20, top: 20 }}
        style={styles.buttonContainer}
        onPress={() => navigation.goBack()}
      >
        <Cross />
      </TouchableOpacity>
    </View>
  );
};

export default MediaPlayer;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    backgroundColor: COLORS.black,
  },
  videoContainer: {
    width: "100%",
    height: "100%",
  },
  buttonContainer: {
    position: "absolute",
    right: 30,
    top: Platform.OS === "android" ? 50 : 100,
    zIndex: 30,
  },
  loadingIndicator: {
    position: "absolute",
    alignSelf: "center",
    top: "45%",
    zIndex: 10,
  },
});
