import { Image, SafeAreaView, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS, sizes } from "../../../themes";
import { Image6, person, Profile } from "../../../assets/images";
import { ArrowLeft, Bell, Danger, Settings1 } from "../../../assets/svgicons";
import { ApprovedAppointment, ButtonwithIcon, EarningDetails, TextButton } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalTabParamList } from "../../../navigation/ProfessionalBottomTab";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { fetchActiveBookingsThunk, fetchCompletedBookingsThunk } from "../../../redux/slices/BookingsSlice";
import FastImage from "react-native-fast-image";
import { getLoggedInUser } from "../../../redux/slices/AuthSlice";
import { INotification } from "../../../models/INotification";
import NotificationServices from "../../../Services/NotificationServices";
import { useFocusEffect } from "@react-navigation/native";
type Props = NativeStackScreenProps<ProfessionalTabParamList, "ProfessionalHome">;

const ProfessionalHome: React.FC<Props> = ({ navigation }) => {
  const { userId, user } = useAppSelector((state) => state.auth);
  const { results: active_bookings } = useAppSelector((state) => state.bookings.active);
  const dispatch = useAppDispatch();

  const [notifications, setNotifications] = useState<INotification[]>([]);
  const fetchUserNotifications = async () => {
    try {
      const response = await NotificationServices.FetchNotificationByid(userId);
      setNotifications(response.data.data.notifications.filter((noti: INotification) => !noti?.read));
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  useEffect(() => {
    if (userId) {
      dispatch(fetchActiveBookingsThunk({ userId, userType: "doctor" }));
      dispatch(fetchCompletedBookingsThunk({ userId, userType: "doctor" }));
    }
  }, [userId]);

  useFocusEffect(
    React.useCallback(() => {
      fetchUserNotifications();
    }, []),
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} />
      {/* HEADER */}
      <View style={styles.header}>
        {/* PROFILE_IMAGE */}
        <FastImage source={user.photo ? { uri: user.photo } : person} style={styles.imageContainer} />
        <View style={styles.innerHeaderContainer}>
          <View>
            {/* NAME */}
            <Text style={styles.name}>{user.name}</Text>
            {/* SPECIALITY */}
            <Text style={styles.specilatyText}>Therapist</Text>
          </View>
          <View style={{ flexDirection: "row", alignItems: "center", gap: 10 }}>
            <ButtonwithIcon
              icon={
                <View style={{ position: "relative" }}>
                  {notifications?.length ? (
                    <Text
                      style={{
                        position: "absolute",
                        top: 0,
                        right: -2,
                        backgroundColor: "red",
                        color: "white",
                        zIndex: 10,
                        paddingHorizontal: 4,
                        borderRadius: 99,
                        fontSize: 8,
                      }}
                    >
                      {notifications.length}
                    </Text>
                  ) : null}
                  <Bell />
                </View>
              }
              onPress={() => navigation.navigate("Notification")}
            />
            <ButtonwithIcon icon={<Settings1 fill={COLORS.black} />} onPress={() => navigation.navigate("Settings")} />
          </View>
        </View>
      </View>
      <ScrollView contentContainerStyle={styles.innerContainer} showsVerticalScrollIndicator={false}>
        {/* APPROVAL_INFO */}
        {!user?.isStripeConnected ? (
          <View style={styles.approvalContainer}>
            <Danger />
            <View style={{ paddingLeft: 12, flex: 1 }}>
              <Text style={styles.approvalText}>Payment Profile!</Text>
              <Text style={styles.text}>You must complete your payment profile to receive payment</Text>
            </View>
            <TouchableOpacity
              style={{ transform: [{ rotate: "180deg" }] }}
              hitSlop={20}
              onPress={() => navigation.navigate("ConnectedAccount")}
            >
              <ArrowLeft fill={COLORS.primary} />
            </TouchableOpacity>
            {/* <TextButton label="Confirm" labelStyle={styles.labelStyles} /> */}
          </View>
        ) : null}
        {/* APPROVAL_INFO */}
        {!user?.isVerified ? (
          <View style={styles.approvalContainer}>
            <Danger />
            <View style={{ paddingLeft: 12, flex: 1 }}>
              <Text style={styles.approvalText}>Your profile approval is pending!</Text>
              <Text style={styles.text}>You will be notified soon</Text>
            </View>
            {/* <TextButton label="Confirm" labelStyle={styles.labelStyles} /> */}
          </View>
        ) : null}

        {/* REQUESTED_AND_ACTIVE_BOOKING_CONTAINER */}
        <View style={styles.requestedContainer}>
          <Text style={styles.requestedText}>Active Bookings</Text>
          <Text style={styles.text1}>{active_bookings?.length || 0}</Text>
        </View>
        <View>
          {/* TODAY_APPOINTMENT */}
          <View style={styles.todayAppointmentContainer}>
            <Text style={styles.appointmentText}>Appointments Today</Text>
            <TouchableOpacity onPress={() => navigation.navigate("ProfessionalBookingList")}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <View>
            {false ? (
              <View style={styles.emptyStateContainer}>
                <Image source={Image6} style={{ width: 200, height: 150 }} />
                <Text style={styles.emptyStateText}>Nothing to show here!</Text>
              </View>
            ) : (
              <View>
                <ApprovedAppointment
                  showRating={false}
                  containerStyle={{ marginBottom: 3, marginHorizontal: 20 }}
                  onViewDetails={(bookingId) => navigation.navigate("ProfessionalBookingDetails", { bookingId })}
                  onStartSession={(sessionId) => navigation.navigate("Session", { sessionId, doctorId: userId })}
                />
              </View>
            )}
          </View>
        </View>
        {/* PRICE_INFO */}
        <EarningDetails onViewDetails={() => navigation.navigate("EarningsDetails")} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfessionalHome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 10,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 0,
    flexDirection: "row",
    alignItems: "center",
    paddingBottom: 8,
  },
  imageContainer: {
    width: 46,
    height: 46,
    borderRadius: 46,
  },
  innerHeaderContainer: {
    flex: 1,
    paddingLeft: 10,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  name: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  specilatyText: {
    fontSize: 11,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Light,
  },
  innerContainer: {
    // paddingHorizontal: 20,
    paddingTop: 16,
  },
  approvalContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FDD83520",
    paddingVertical: 12,
    paddingHorizontal: 14,
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  approvalText: {
    fontSize: 12,
    fontFamily: FONTS.Nunito_Bold,
    color: COLORS.black,
  },
  text: {
    fontSize: 11,
    fontFamily: FONTS.Nunito_Light,
    color: COLORS.black,
  },
  labelStyles: {
    fontSize: 12,
    color: "#EB4E1F",
    fontFamily: FONTS.Nunito_Bold,
  },
  requestedContainer: {
    alignItems: "flex-start",
    justifyContent: "center",
    borderRadius: 10,
    height: 90,
    paddingLeft: 15,
    borderWidth: 1,
    marginHorizontal: 20,
    backgroundColor: "#EFFFED",
    borderColor: "#CFFFDA",
  },
  requestedText: {
    fontSize: 14,
    color: "#5A5A5A",
    fontFamily: FONTS.Nunito_Medium,
  },
  text1: {
    color: "#292929",
    fontSize: 15,
    fontFamily: FONTS.Nunito_SemiBold,
    paddingTop: 5,
  },
  todayAppointmentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 27,
    paddingBottom: 16,
    marginHorizontal: 20,
  },
  appointmentText: {
    fontSize: 16,
    color: "#030F1C",
    fontFamily: FONTS.Nunito_Bold,
  },
  viewAllText: {
    fontSize: 14,
    color: "#030F1C",
    fontFamily: FONTS.Nunito_Regular,
  },

  emptyStateContainer: {
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    paddingVertical: 16,
    backgroundColor: COLORS.white,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  emptyStateText: {
    fontSize: 14,
    color: "#969696",
    fontFamily: FONTS.Nunito_Regular,
    paddingTop: 12,
  },
});
