import { ActivityIndicator, Dimensions, SectionList, StatusBar, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { NotificationCard } from "../../../components";
import { COLORS, FONTS } from "../../../themes";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { useAppSelector } from "../../../redux/config/Store";
import NotificationServices from "../../../Services/NotificationServices";
import { INotification } from "../../../models/INotification";
import moment from "moment";
import { groupNotifications } from "../../../helper/groupNotifications";

type Props = NativeStackScreenProps<HomeStackParamList, "Notification">;

const Notification: React.FC<Props> = () => {
  const user = useAppSelector((state) => state.auth.user);
  const [sections, setSections] = useState<{ title: string; data: INotification[] }[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchUserNotifications = async () => {
    try {
      const response = await NotificationServices.FetchNotificationByid(user._id);
      const notifications = response?.data?.data?.notifications || [];

      // Group notifications into sections
      const groupedData = groupNotifications(notifications);
      setSections(groupedData);
      setLoading(false);
      NotificationServices.MarkAllNotificationAsRead(user._id);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserNotifications();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.red} />
      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={{ color: "#000" }}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <SectionList
          showsVerticalScrollIndicator={false}
          sections={sections}
          keyExtractor={(item, index) => item.body + index}
          contentContainerStyle={{ paddingBottom: 30 }}
          renderSectionHeader={({ section: { title } }) => <Text style={styles.title}>{title}</Text>}
          stickySectionHeadersEnabled={false} // Allows headers to scroll away with content
          renderItem={({ item }) => (
            <NotificationCard
              containerStyle={{ marginBottom: 14 }}
              body={item.body}
              title={item.title}
              createdAt={moment(item.createdAt).format("MM/DD/YY")}
            />
          )}
          ListEmptyComponent={
            <View
              style={{
                flex: 1,
                width: "100%",
                height: Dimensions.get("screen").height - 300,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ fontFamily: FONTS.Nunito_Bold, color: COLORS.black }}>No notifications yet..</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

export default Notification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  title: {
    fontSize: 14,
    fontWeight: "bold",
    color: COLORS.black,
    marginTop: 28,
    marginLeft: 16,
    marginBottom: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 10,
  },
});
