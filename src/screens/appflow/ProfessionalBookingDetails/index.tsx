import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect } from "react";
import { COLORS, FONTS } from "../../../themes";
import { ButtonwithIcon, Review, TextButton } from "../../../components";
import { Calendar4, Message1 } from "../../../assets/svgicons";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import moment from "moment";
import { useAppSelector } from "../../../redux/config/Store";

type Props = NativeStackScreenProps<ProfessionalStackParamList, "ProfessionalBookingDetails">;

const ProfessionalBookingDetails: React.FC<Props> = ({ navigation, route }) => {
  const { bookingId } = route.params;
  const { results: active_bookings } = useAppSelector((state) => state.bookings.active);
  const { results: completed_bookings } = useAppSelector((state) => state.bookings.completed);
  const booking = [...active_bookings, ...completed_bookings].find((item) => item._id === bookingId);
  const empty_avatar =
    "https://img.freepik.com/premium-vector/default-avatar-profile-icon-social-media-user-image-gray-avatar-icon-blank-profile-silhouette-vector-illustration_561158-3383.jpg?semt=ais_hybrid";

  const isWithinSessionTime = booking
    ? (() => {
        const bookingDateTime = moment(
          `${booking.bookingDateTime.date} ${booking.bookingDateTime.time}`,
          "YYYY-MM-DD HH:mm",
        );
        const sessionEndTime = bookingDateTime.clone().add(1, "hour");
        const now = moment();
        return now.isBetween(bookingDateTime, sessionEndTime, null, "[]");
      })()
    : false;

  useEffect(() => {
    navigation.setOptions({
      headerTitle: booking?.status === "active" ? "Bookings" : "Past Bookings",
      headerRight: () =>
        booking?.status === "pending" ? null : (
          <TouchableOpacity
            onPress={() => navigation.navigate("ProfessionalAgendaView")}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Calendar4 />
          </TouchableOpacity>
        ),
    });
  }, [navigation, route.params, booking?.status]);
  return (
    <View style={styles.container}>
      {/* APPOINTED_CARD */}
      <View style={styles.card}>
        <Text style={styles.text}>Appointment:</Text>
        <Text style={[styles.title, { marginTop: 14 }]}>{booking?.service?.name}</Text>
        <Text style={[styles.text, { marginTop: 8 }]}>1 hour @ ${booking?.service?.price}</Text>
      </View>
      {/* DOCTOR */}
      <View style={[styles.card, { marginTop: 16 }]}>
        <Text style={[styles.text, { marginBottom: 14 }]}>Patient:</Text>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Image
            source={{ uri: booking?.patient?.photo || empty_avatar }}
            style={{ width: 32, height: 32, borderRadius: 32 / 2 }}
          />
          <Text style={[styles.title, { marginLeft: 7, flex: 1 }]}>{booking?.patient?.name}</Text>
          {/* MESSAGE_BUTTON */}
          <ButtonwithIcon
            icon={<Message1 />}
            onPress={() =>
              navigation.navigate("ChatDetails", {
                otherUser: booking.patient,
              })
            }
          />
        </View>
      </View>
      {/* TIME */}
      <View style={[styles.card, { marginTop: 16 }]}>
        <Text style={styles.text}>Date & Time:</Text>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Text style={styles.title}>{moment(booking?.bookingDateTime?.date).format("MMM DD, dddd")}</Text>
          <View style={styles.dot} />
          <Text style={styles.title}>
            <Text style={styles.title}>{booking?.bookingDateTime?.time}</Text>
          </Text>
        </View>
      </View>
      {/* MODE */}
      {/* <View style={[styles.card, {marginTop: 16}]}>
        <Text style={styles.text}>Mode:</Text>
        <View style={{flexDirection: 'row', marginTop: 15}}>
          <ButtonwithIcon icon={<Call1 />} disabled />
          <ButtonwithIcon
            disabled
            icon={<VideoCall />}
            containerStyle={{marginLeft: 12}}
            onPress={() => {
              // if (isBookingActive()) {
              // navigation.navigate('Session', {sessionId: bookingId});
              // } else {
              //   Alert.alert(
              //     'Session Inactive',
              //     'The session time has either passed or not started yet.',
              //   );
              // }
            }}
          />
        </View>
      </View> */}
      {booking?.status == "completed" &&
        (booking?.review?.review ? (
          <Review rating={booking?.review?.rating.toString()} reviewText={booking?.review?.review} />
        ) : null)}

      {booking?.status === "active" && (
        <View style={styles.footer}>
          <TextButton
            label={"Start Session"}
            containerStyle={styles.startSessionButton}
            onPress={() => navigation.navigate("Session", { sessionId: booking._id })}
          />
        </View>
      )}
    </View>
  );
};

export default ProfessionalBookingDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
  },
  card: {
    backgroundColor: "#FBFBFB",
    padding: 10,
    borderRadius: 10,
  },
  text: {
    fontSize: 11,
    color: "#263238",
    fontFamily: FONTS.Nunito_Regular,
  },
  title: {
    fontSize: 15,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  dot: {
    width: 7,
    height: 7,
    borderRadius: 7 / 2,
    backgroundColor: "#D9D9D9",
    marginHorizontal: 13,
  },
  scheduleText: {
    fontSize: 14,
    color: "#EB4E1F",
    marginTop: 10,
    fontFamily: FONTS.Nunito_Bold,
  },
  startSessionButton: {
    height: 48,
    backgroundColor: COLORS.red,
    marginTop: 28,
    borderRadius: 8,
    borderColor: COLORS.red,
  },
  cancleButton: {
    width: "25%",
    height: 20,
  },
  footer: {
    flex: 1,
    justifyContent: "space-between",
    marginBottom: 30,
  },
  paragraph: {
    fontSize: 12,
    color: "#263238",
    textAlign: "center",
    paddingTop: 20,
    fontFamily: FONTS.Nunito_Regular,
  },
});
