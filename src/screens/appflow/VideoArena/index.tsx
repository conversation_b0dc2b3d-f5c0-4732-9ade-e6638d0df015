import { <PERSON>ivity<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, FlatList, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS, FONTS } from "../../../themes";
import { Body_Parts, BlogCard, TreatmentVideoItem, ProgramItem } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { BottomStackParamList } from "../../../navigation/BottomTab";
import VideoArenaTabs from "./VideoArenaTabs";
type Props = NativeStackScreenProps<BottomStackParamList, "VideoArena">;
import firestore from "@react-native-firebase/firestore";
import { IBlogs } from "../../../models/IBlog";
import { IExercise, IProgram, ITreatmentVideos } from "../../../models/IProgram";
import { useAppSelector } from "../../../redux/config/Store";
import { getCategoriesForUI } from "../../../Services/CategoriesService";

const VideoArena: React.FC<Props> = ({ navigation }) => {
  const userId = useAppSelector((state) => state.auth.userId);
  const [selectedBodyPart, setSelectedBodyPart] = useState("all");
  const [activeTab, setActiveTab] = useState<"program" | "free" | "joined">("program");
  const [programs, setPrograms] = useState<IProgram[]>([]);
  const [blogs, setBlogs] = useState<IBlogs[]>([]);
  const [loading, setLoading] = useState(true);
  const [blogLoading, setBlogLoading] = useState(true);
  const [treatmentLoading, setTreatmentLoading] = useState(true);
  const [treatmentVideos, setTreatmentVideos] = useState<ITreatmentVideos[]>([]);
  const [addNewProgramLoading, setAddNewProgramLoading] = useState<string>("");
  const [bodyParts, setBodyParts] = useState<Array<{ key: string; label: string }>>([]);

  // Fetch categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categories = await getCategoriesForUI();
        setBodyParts(categories);
      } catch (error) {
        console.error("Error loading categories:", error);
      }
    };
    loadCategories();
  }, []);

  const fetchPrograms = () => {
    setLoading(true);

    const query =
      selectedBodyPart !== "all"
        ? firestore().collection("Programs").where("type", "==", selectedBodyPart)
        : firestore().collection("Programs");

    const unsubscribe = query.onSnapshot(
      (snapshot) => {
        const data = snapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as IProgram[];
        setPrograms(data);
        setLoading(false);
      },
      (error) => {
        console.error("Error fetching programs:", error);
        setLoading(false);
      },
    );

    return unsubscribe; // Return unsubscribe function to clean up the listener
  };

  const fetchBlogs = async () => {
    setBlogLoading(true);
    try {
      const snapshot = await firestore().collection("Blogs").limit(1).get();
      const fetchedBlogs = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as IBlogs[];
      setBlogs(fetchedBlogs);
    } catch (error) {
      console.error("Error fetching blogs:", error);
    } finally {
      setBlogLoading(false);
    }
  };
  const fetchTreatmentVideos = async () => {
    setBlogLoading(true);
    try {
      const snapshot = await firestore().collection("TreatmentVideos").limit(1).get();
      const treamentVideos = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as ITreatmentVideos[];
      setTreatmentVideos(treamentVideos);
    } catch (error) {
      console.error("Error fetching blogs:", error);
    } finally {
      setTreatmentLoading(false);
    }
  };

  const addNewProgram = async (programId: string) => {
    setAddNewProgramLoading(programId);
    try {
      await firestore()
        .collection("Programs")
        .doc(programId)
        .update({
          subscriber: firestore.FieldValue.arrayUnion(userId),
        });
      await firestore().collection("UserPrograms").add({
        programId: programId,
        userId: userId,
        completedExercises: [],
      });
    } catch (error) {
      console.log("error while subcribing a program", error);
    } finally {
      setAddNewProgramLoading("");
    }
  };
  const fetchAssignedPrograms = async () => {
    setLoading(true);
    try {
      const query =
        selectedBodyPart !== "all"
          ? firestore()
              .collection("AssignedPrograms")
              .where("patientId", "==", userId)
              .where("programData.type", "==", selectedBodyPart)
          : firestore().collection("AssignedPrograms").where("patientId", "==", userId);
      const snapshot = await query.get();
      console.log("type", selectedBodyPart);
      const data = snapshot.docs.map((doc) => {
        const d = doc.data().programData as IProgram;
        return d;
      });
      console.log("Assigned Programs Data -------------------", data);
      setPrograms(data);
    } catch (error) {
      console.error("Error fetching assigned programs:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (activeTab === "joined") {
      fetchAssignedPrograms();
    }
  }, [selectedBodyPart, activeTab]);
  useEffect(() => {
    if (activeTab === "program") {
      const unsubscribe = fetchPrograms();
      return () => unsubscribe(); //
    }
  }, [selectedBodyPart, activeTab]);

  useEffect(() => {
    activeTab === "free" ? fetchTreatmentVideos() : null;
  }, [activeTab]);

  useEffect(() => {
    activeTab === "free" ? fetchBlogs() : null;
  }, [activeTab]);

  return (
    <View style={styles.container}>
      <VideoArenaTabs activeTab={activeTab} setActiveTab={setActiveTab} />
      <View style={{ flex: 1, marginTop: 24 }}>
        {activeTab === "program" || activeTab === "joined" ? (
          <>
            <View style={{ marginBottom: 24 }}>
              <FlatList
                data={bodyParts}
                showsHorizontalScrollIndicator={false}
                horizontal
                contentContainerStyle={{
                  gap: 8,
                  paddingHorizontal: 20,
                }}
                renderItem={({ item }) => {
                  return (
                    <Body_Parts
                      label={item.label}
                      backgroundColor={selectedBodyPart == item.key ? "#114EBE" : "#FAFCFF"}
                      labelColor={selectedBodyPart == item.key ? COLORS.white : "#114EBE"}
                      borderColor={selectedBodyPart == item.key ? COLORS.white : "#114EBE50"}
                      onPress={() => setSelectedBodyPart(item.key)}
                    />
                  );
                }}
              />
            </View>

            {loading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.paragraph}>Just a moment..</Text>
                <ActivityIndicator color={COLORS.primary} />
              </View>
            ) : (
              <FlatList
                data={programs}
                contentContainerStyle={{
                  paddingHorizontal: 20,
                  paddingBottom: 70,
                  gap: 16,
                }}
                ListEmptyComponent={
                  <View style={styles.loadingContainer}>
                    <Text style={styles.paragraph}>No Video found</Text>
                  </View>
                }
                showsVerticalScrollIndicator={false}
                renderItem={({ item }) => {
                  return (
                    <ProgramItem
                      imageUrl={item.thumbnail}
                      duration={item.duration}
                      numberofExercises={item?.exercises?.length}
                      name={item.name}
                      type={item.type}
                      showButton={true}
                      onPressProgramItem={() =>
                        navigation.navigate("ProgramDetails", {
                          programId: item.id,
                          isAssigned: activeTab === "joined",
                        })
                      }
                      onPressAddProgram={() => addNewProgram(item.id)}
                      isMyCourse={activeTab === "joined" ? true : item?.subscriber?.includes(userId)}
                      addNewProgramLoading={addNewProgramLoading === item.id ? true : false}
                    />
                  );
                }}
              />
            )}
          </>
        ) : blogLoading && treatmentLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.paragraph}>Just a moment..</Text>
            <ActivityIndicator color={COLORS.primary} />
          </View>
        ) : (
          <>
            <View style={styles.header}>
              <Text style={styles.heading}>Blogs</Text>
              <TouchableOpacity onPress={() => navigation.navigate("Blogs")}>
                <Text style={styles.viewAllText}>View all</Text>
              </TouchableOpacity>
            </View>
            <View>
              <FlatList
                data={blogs}
                contentContainerStyle={{ gap: 16 }}
                renderItem={({ item }) => (
                  <BlogCard
                    title={item.title}
                    about={item.about}
                    thumbnail={item.thumbnail}
                    onPress={() => navigation.navigate("BlogsDetails", { blog: item })}
                  />
                )}
              />
            </View>
            <View style={[styles.header, { marginTop: 14 }]}>
              <Text style={styles.heading}>Treatment Videos</Text>
              <TouchableOpacity onPress={() => navigation.navigate("TreatmentVideos")}>
                <Text style={styles.viewAllText}>View all</Text>
              </TouchableOpacity>
            </View>
            <FlatList
              data={treatmentVideos}
              contentContainerStyle={{ gap: 16 }}
              renderItem={({ item }) => (
                <TreatmentVideoItem
                  name={item.name}
                  onPress={() =>
                    navigation.navigate("MediaPlayer", {
                      videoUrl: item.videoUrl,
                      exerciseId: "",
                      programId: "",
                    })
                  }
                  duration={item.duration}
                  imageUrl={item.thumbnail}
                />
              )}
            />
          </>
        )}
      </View>
    </View>
  );
};

export default VideoArena;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 8,
  },
  title: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  lineStyles: {
    width: "100%",
    height: 4,
    backgroundColor: "#114EBE",
    marginTop: 9,
  },

  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginBottom: 14,
  },
  heading: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
  },
  loadingContainer: {
    paddingTop: 80,
    flexDirection: "row",
    alignSelf: "center",
    gap: 10,
  },
  paragraph: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Medium,
  },
  viewAllText: {
    fontSize: 12,
    fontFamily: FONTS.Nunito_Regular,
    color: COLORS.black,
  },
});
