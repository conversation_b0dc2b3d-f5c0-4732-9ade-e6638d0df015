import { Dimensions, StyleSheet, Text, TouchableOpacity, View, Animated } from "react-native";
import React, { useRef, useEffect } from "react";
import { COLORS, FONTS } from "../../../themes";

interface Props {
  activeTab: string;
  setActiveTab: (val: "program" | "free" | "joined") => void;
}

const VideoArenaTabs: React.FC<Props> = ({ activeTab, setActiveTab }) => {
  const screenWidth = Dimensions.get("screen").width;
  const tabWidth = screenWidth / 3 - 2;

  // Animation logic
  const translateX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const targetIndex = activeTab === "free" ? 1 : activeTab === "joined" ? 2 : 0;

    Animated.timing(translateX, {
      toValue: targetIndex * tabWidth,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [activeTab, tabWidth]);

  // Tab labels
  const tabs = ["program", "free", "joined"];

  return (
    <View style={styles.tabsContainer}>
      <Animated.View
        style={[
          styles.indicator,
          {
            width: tabWidth,
            transform: [{ translateX }],
          },
        ]}
      />
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabContainer, { width: tabWidth }]}
          onPress={() => setActiveTab(tab as "program" | "free" | "joined")}
        >
          <Text style={[styles.tabText]}>{tab}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default VideoArenaTabs;

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: "row",
    position: "relative",
    paddingVertical: 4,
  },
  tabContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
  },
  tabText: {
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Bold,
    textTransform: "capitalize",
  },
  indicator: {
    position: "absolute",
    height: 4,
    backgroundColor: "#114EBE",
    marginTop: 9,
    bottom: 0,
  },
});
