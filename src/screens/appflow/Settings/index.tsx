import { Image, StatusBar, StyleSheet, View } from "react-native";
import React, { FC, useState } from "react";
import { COLORS } from "../../../themes";
import { Bell1, DelProfile, User1 } from "../../../assets/svgicons";
import { SettingAction } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { ProfessionalStackParamList } from "../../../navigation/ProfessionalStack";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import ConfirmModal from "../../../components/ConfirmModal/ConfirmModal";
import { setLogoutAction, updateUser } from "../../../redux/slices/AuthSlice";
import { bookingResetAction } from "../../../redux/slices/BookingsSlice";
import UserService from "../../../Services/UserService";
import { showToast } from "../../../helper/toast";
import AuthServices from "../../../Services/AuthServices";

type Props = NativeStackScreenProps<HomeStackParamList & ProfessionalStackParamList, "Settings">;

const Settings: FC<Props> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const [isSwitchOn, setIsSwitchOn] = useState<boolean>(user.receiveNotifications);

  const [isDeleteModalVisible, setDeleteModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const toggleDeleteModal = () => setDeleteModalVisible(!isDeleteModalVisible);
  const onPressDeleteAccount = async () => {
    console.log("response.datah");
    setLoading(true);
    try {
      await UserService.DeleteUserAccount(user._id).then((response) => {
        toggleDeleteModal();
        dispatch(setLogoutAction());
        dispatch(bookingResetAction());
        setLoading(false);
        showToast("Your account has been successfully deleted.", "Success", "success");
      });
    } catch (error) {
      console.log("error while deleting user account");
      showToast("Something went wrong. Please try again.");
      setLoading(false);
      toggleDeleteModal();
    }
  };
  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} />
      <SettingAction
        label="Edit Profile"
        icon={<User1 />}
        onPress={() => {
          console.log("user?.userType", user?.userType);
          if (user?.userType === "doctor") {
            navigation.navigate("EditDoctorDetails");
          } else if (user?.userType === "patient") {
            navigation.navigate("EditPatientProfile");
          }
        }}
      />
      <SettingAction
        icon={<Bell1 />}
        label={"Push Notifications"}
        showSwitch={true}
        isSwitchOn={isSwitchOn}
        setIsSwitchOn={(val) => {
          setIsSwitchOn(val);
          dispatch(updateUser({ receiveNotifications: val }));
          AuthServices.UpdateUser(user._id, { data: { receiveNotifications: val } });
        }}
      />

      <SettingAction icon={<DelProfile />} label={"Delete your Account"} onPress={toggleDeleteModal} />
      <ConfirmModal
        isVisible={isDeleteModalVisible}
        onClose={toggleDeleteModal}
        onConfirm={onPressDeleteAccount}
        title="Delete Account?"
        message="Are you sure you want to delete your account? This action cannot be undone."
        confirmLabel="Yes, Delete my account"
        loading={loading}
      />
    </View>
  );
};

export default Settings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 30,
    paddingHorizontal: 20,
  },
});
