import { ActivityIndicator, RefreshControl, ScrollView, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { COLORS } from "../../../themes";
import { ProfessionalBookingCard, BookingTabs, ButtonwithIcon, EmptyState } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { ProfessionalTabParamList } from "../../../navigation/ProfessionalBottomTab";
import { useAppDispatch, useAppSelector } from "../../../redux/config/Store";
import { fetchActiveBookingsThunk, fetchCompletedBookingsThunk } from "../../../redux/slices/BookingsSlice";
import { Calendar } from "../../../assets/images";
import { Calendar4 } from "../../../assets/svgicons";
type Props = NativeStackScreenProps<ProfessionalTabParamList, "ProfessionalBookingList">;

const ProfessionalBookingList: React.FC<Props> = ({ navigation }) => {
  const [selectedTab, setSelectedTab] = useState<"Active" | "Past">("Active");

  const dispatch = useAppDispatch();
  const { userId } = useAppSelector((state) => state.auth);
  const { results: active_bookings, loading } = useAppSelector((state) => state.bookings.active);
  const { results: completed_bookings } = useAppSelector((state) => state.bookings.completed);
  const [refreshing] = useState(false);

  const results = selectedTab === "Active" ? active_bookings : completed_bookings;

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <ButtonwithIcon
          icon={<Calendar4 />}
          onPress={() => navigation.navigate("ProfessionalAgendaView")}
          containerStyle={{ marginRight: 20 }}
        />
      ),
    });
  }, [navigation]);

  const onPullToRefresh = () => {
    dispatch(fetchActiveBookingsThunk({ userId, userType: "doctor" }));
    dispatch(fetchCompletedBookingsThunk({ userId, userType: "doctor" }));
  };

  return (
    <View style={styles.container}>
      {/* TABS */}
      <BookingTabs activeTab={selectedTab} setActiveTab={setSelectedTab} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <Text style={{ color: "#000" }}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : null}

      {!loading ? (
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onPullToRefresh}
              colors={["#ff0000", "#00ff00", "#0000ff"]} // Android: Red, Green, Blue
              tintColor="#ff0000" // iOS: Red
            />
          }
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.innerContainer}>
            {results?.length ? (
              results.map((item, i) => (
                <ProfessionalBookingCard
                  key={i}
                  booking={item}
                  onPressViewDetails={() =>
                    navigation.navigate("ProfessionalBookingDetails", {
                      bookingId: item._id,
                    })
                  }
                  onPressSessionStart={() => navigation.navigate("Session", { sessionId: item?._id })}
                />
              ))
            ) : (
              <EmptyState image={Calendar} title={"You have no past booking"} />
            )}
          </View>
        </ScrollView>
      ) : null}
    </View>
  );
};

export default ProfessionalBookingList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 10,
  },
  innerContainer: {
    flex: 1,
    marginHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    gap: 10,
  },
});
