import {
  <PERSON><PERSON><PERSON>iew,
  StatusBar,
  StyleSheet,
  View,
  useWindowDimensions,
} from 'react-native';
import React, {FC} from 'react';
import {COLORS} from '../../../themes';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import RenderHTML from 'react-native-render-html';

type Props = NativeStackScreenProps<HomeStackParamList, 'AboutUs'>;

const AboutUs: FC<Props> = () => {
  const {width} = useWindowDimensions();

  const aboutUsHtml = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>About Us - OW</title>
      <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
      <style>
          body {
              font-family: 'Nunito', sans-serif;
              line-height: 1.6;
              margin: 0;
              padding: 20px;
              background-color: #f9f9f9;
              color: #333;
          }
          h1, h2, h3 {
              color: #222;
          }
          h1 {
              font-size: 2em;
              margin-bottom: 20px;
          }
          h2 {
              font-size: 1.5em;
              margin-top: 20px;
              margin-bottom: 10px;
          }
          p {
              margin-bottom: 15px;
          }
          ul {
              margin-left: 20px;
          }
          ul li {
              margin-bottom: 10px;
          }
          a {
              color: #007bff;
              text-decoration: none;
          }
          a:hover {
              text-decoration: underline;
          }
      </style>
  </head>
  <body>
      <h1>About Us</h1>
      <p>Welcome to OW! Our mission is to connect people and make their lives easier by providing innovative solutions. Here's a little more about who we are and what we do.</p>
      <h2>Our Mission</h2>
      <p>We aim to simplify your daily life by offering a platform that connects people with resources, services, and opportunities.</p>
      <h2>Our Vision</h2>
      <p>To be the leading platform where people can find everything they need, all in one place. Our focus is on building trust, ensuring convenience, and fostering community.</p>
      <h2>What We Do</h2>
      <ul>
          <li>Provide seamless services for individuals and businesses.</li>
          <li>Enhance accessibility and ease of use through technology.</li>
          <li>Create opportunities for growth and collaboration.</li>
      </ul>
      <h2>Our Values</h2>
      <p>At OW, we believe in:</p>
      <ul>
          <li><strong>Integrity:</strong> Transparency in all we do.</li>
          <li><strong>Innovation:</strong> Continuously improving our platform.</li>
          <li><strong>Customer Focus:</strong> Putting our users at the heart of every decision.</li>
      </ul>
      <h2>Meet the Team</h2>
      <p>Our team is composed of passionate individuals dedicated to making your experience exceptional. From developers to customer support, we’re here for you.</p>
      <h2>Get in Touch</h2>
      <p>We love hearing from our users! If you have questions, feedback, or just want to say hello, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
      <p>Thank you for choosing OW. Together, we can make a difference!</p>
  </body>
  </html>
  `;

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      <ScrollView
        contentContainerStyle={{paddingBottom: 32}}
        showsVerticalScrollIndicator={false}>
        <RenderHTML
          contentWidth={width}
          source={{html: aboutUsHtml}}
          baseStyle={{color: 'black'}} // Ensures all text remains black
        />
      </ScrollView>
    </View>
  );
};

export default AboutUs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 10,
    paddingHorizontal: 20,
  },
});
