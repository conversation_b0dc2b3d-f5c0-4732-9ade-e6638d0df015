import { ActivityIndicator, FlatList, StatusBar, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { appStyles, COLORS, FONTS } from "../../../themes";
import { ExerciseItem } from "../../../components";
import type { NativeStackScreenProps } from "@react-navigation/native-stack";
import { HomeStackParamList } from "../../../navigation/HomeStack";
import { IExercise, IProgram } from "../../../models/IProgram";
import { firebase } from "@react-native-firebase/firestore";
type Props = NativeStackScreenProps<HomeStackParamList, "ProgramDetails">;

const ProgramDetails: React.FC<Props> = ({ navigation, route }) => {
  const { programId, isAssigned } = route.params;
  const [selectedTab, setSelectedTab] = useState("week1");
  const [program, setProgram] = useState<IProgram>();
  const [loading, setLoading] = useState(true);
  const [exercises, setExercises] = useState<IExercise[]>([]);

  useEffect(() => {
    console.log("--- Check is Assigned", isAssigned);
    if (isAssigned) {
      fetchAssignedPrograms();
    } else {
      fetchProgramById();
    }
  }, [programId, isAssigned]);

  const fetchProgramById = async () => {
    setLoading(true);
    try {
      const doc = await firebase.firestore().collection("Programs").doc(programId).get();
      if (doc.exists) {
        const data = { id: doc.id, ...doc.data() } as IProgram;

        const exerciseIds = data.exercises || [];

        const exercisePromises = exerciseIds.map(async (exerciseId) => {
          const exerciseDoc = await firebase.firestore().collection("Exercises").doc(exerciseId).get();
          return exerciseDoc.exists ? ({ id: exerciseDoc.id, ...exerciseDoc.data() } as IExercise) : null;
        });

        const exercises = (await Promise.all(exercisePromises)).filter((exercise) => exercise !== null) as IExercise[];

        setProgram({ ...data });
        setExercises(exercises);
      } else {
        console.log("Program not found");
      }
    } catch (error) {
      console.error("Error fetching program:", error);
    } finally {
      setLoading(false);
    }
  };
  const fetchAssignedPrograms = async () => {
    setLoading(true);
    try {
      const doc = await firebase.firestore().collection("AssignedPrograms").doc(programId).get();
      if (doc.exists) {
        const data = { id: doc.id, ...doc.data().programData } as IProgram;

        const exerciseIds = data.exercises || [];

        const exercisePromises = exerciseIds.map(async (exerciseId) => {
          const exerciseDoc = await firebase.firestore().collection("Exercises").doc(exerciseId).get();
          return exerciseDoc.exists ? ({ id: exerciseDoc.id, ...exerciseDoc.data() } as IExercise) : null;
        });

        const exercises = (await Promise.all(exercisePromises)).filter((exercise) => exercise !== null) as IExercise[];

        setProgram({ ...data });
        setExercises(exercises);
      } else {
        console.log("Assigned Program not found");
      }
    } catch (error) {
      console.error("Error fetching assigned program:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} />

      {/* HEADER */}
      {/* <View style={styles.headerContainer}>
        <Tabs
          label={'Week 1'}
          ActiveTabColor={selectedTab == 'week1' ? COLORS.red : '#515151'}
          labelColor={selectedTab == 'week1' ? COLORS.red : '#515151'}
          onPress={() => setSelectedTab('week1')}
        />
        <Tabs
          label={'Week 2'}
          ActiveTabColor={selectedTab == 'week2' ? COLORS.red : '#515151'}
          labelColor={selectedTab == 'week2' ? COLORS.red : '#515151'}
          onPress={() => setSelectedTab('week2')}
        />
        <Tabs
          label={'Week 3'}
          ActiveTabColor={selectedTab == 'week3' ? COLORS.red : '#515151'}
          labelColor={selectedTab == 'week3' ? COLORS.red : '#515151'}
          onPress={() => setSelectedTab('week3')}
        />
      </View> */}
      {loading ? (
        <View style={appStyles.loadingContainer}>
          <Text style={appStyles.loadingParagraph}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={exercises}
          ListHeaderComponent={<Text style={styles.title}>{program?.exercises?.length} Exercises</Text>}
          contentContainerStyle={{ gap: 16 }}
          renderItem={({ item, index }) => {
            return (
              <ExerciseItem
                imageUrl={item.thumbnail}
                onPressItem={() =>
                  navigation.navigate("MediaPlayer", {
                    videoUrl: item.videoUrl,
                    programId: programId,
                    exerciseId: item.id,
                    isAssigned: isAssigned,
                  })
                }
                exerciseName={item.exerciseName}
                duration={item.duration}
                containerStyle={{
                  marginBottom: exercises.length - 1 == index ? 4 : 0,
                }}
              />
            );
          }}
        />
      )}
    </View>
  );
};

export default ProgramDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 25,
  },

  title: {
    fontSize: 16,
    color: COLORS.black,
    marginTop: 24,
    fontFamily: FONTS.Nunito_Bold,
  },
});
