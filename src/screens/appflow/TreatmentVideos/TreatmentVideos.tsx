import {
  FlatList,
  StyleSheet,
  Text,
  View,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {TreatmentVideoItem} from '../../../components';
import {firebase} from '@react-native-firebase/firestore';
import {appStyles, COLORS} from '../../../themes';
import {ITreatmentVideos} from '../../../models/IProgram';

const TreatmentVideos = ({navigation}: any) => {
  const [treatmentVideos, setTreatmentVideos] = useState<ITreatmentVideos[]>(
    [],
  );
  const [loading, setLoading] = useState(true);

  const fetchTreatmentVideos = async () => {
    setLoading(true);
    try {
      const snapshot = await firebase
        .firestore()
        .collection('TreatmentVideos')
        .get();
      const fetchedVideos = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as ITreatmentVideos[];
      setTreatmentVideos(fetchedVideos);
    } catch (error) {
      console.error('Error fetching treatment videos:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTreatmentVideos();
  }, []);

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={appStyles.loadingContainer}>
          <Text style={appStyles.loadingParagraph}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={treatmentVideos}
          contentContainerStyle={{gap: 16, paddingBottom: 32}}
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => (
            <TreatmentVideoItem
              name={item.name}
              duration={item.duration}
              imageUrl={item.thumbnail}
              onPress={() =>
                navigation.navigate('MediaPlayer', {
                  videoUrl: item.videoUrl,
                })
              }
            />
          )}
          keyExtractor={item => item.id}
        />
      )}
    </View>
  );
};

export default TreatmentVideos;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.black,
    marginBottom: 10,
  },
});
