import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {appStyles, COLORS} from '../../../themes';
import {BlogCard} from '../../../components';
import {IBlogs} from '../../../models/IBlog';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import {firebase} from '@react-native-firebase/firestore';
type Props = NativeStackScreenProps<HomeStackParamList, 'Blogs'>;

const Blogs: React.FC<Props> = ({navigation}) => {
  const [blogs, setBlogs] = useState<IBlogs[]>([]);
  const [blogLoading, setBlogLoading] = useState(true);

  const fetchBlogs = async () => {
    setBlogLoading(true);
    try {
      const snapshot = await firebase.firestore().collection('Blogs').get();
      const fetchedBlogs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as IBlogs[];
      setBlogs(fetchedBlogs);
    } catch (error) {
      console.error('Error fetching blogs:', error);
    } finally {
      setBlogLoading(false);
    }
  };

  useEffect(() => {
    fetchBlogs();
  }, []);
  return (
    <View style={styles.container}>
      {blogLoading ? (
        <View style={appStyles.loadingContainer}>
          <Text style={appStyles.loadingParagraph}>Just a moment..</Text>
          <ActivityIndicator color={COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={blogs}
          contentContainerStyle={{gap: 16}}
          renderItem={({item}) => (
            <BlogCard
              title={item.title}
              about={item.about}
              thumbnail={item.thumbnail}
              onPress={() => navigation.navigate('BlogsDetails', {blog: item})}
              containerStyle={{marginHorizontal: 0}}
            />
          )}
        />
      )}
    </View>
  );
};

export default Blogs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
});
