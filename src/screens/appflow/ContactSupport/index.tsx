import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  TextInput,
  Platform,
  StatusBar,
} from 'react-native';
import React, {FC, useState} from 'react';
import {COLORS, FONTS} from '../../../themes';
import FormInput from '../../../components/FormInput';
import {Email, User2} from '../../../assets/svgicons';
import {TextButton} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useAppSelector} from '../../../redux/config/Store';
import {showToast} from '../../../helper/toast';
import UserService from '../../../Services/UserService';

type Props = NativeStackScreenProps<HomeStackParamList, 'ContactSupport'>;

const ContactSupport: FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.auth.user);
  const [loading, setLoading] = useState(false);

  // Formik Hook
  const formik = useFormik({
    initialValues: {
      name: user.name,
      email: user.email,
      message: '',
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required('Name is required'),
      email: Yup.string().email('Invalid email').required('Email is required'),
      message: Yup.string().required('Message is required'),
    }),
    onSubmit: async values => {
      setLoading(true);
      try {
        await UserService.ContactSupportAPI(
          values.name,
          values.email,
          values.message,
        ).then(() => {
          showToast('Your message has been sent!', 'Success', 'success');
          navigation.goBack();
        });
      } catch (error) {
        showToast('Something went wrong. Please try again.');
        navigation.goBack();
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.white} barStyle="dark-content" />

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* NAME INPUT */}
        <FormInput
          placeholder="Name"
          leftIcon={<User2 />}
          placeholderTextColor={'#292D32'}
          containerStyle={{borderColor: '#ECECEC'}}
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          errorMessage={formik.touched.name ? formik.errors.name : undefined}
        />

        {/* EMAIL INPUT */}
        <FormInput
          placeholder="Email"
          leftIcon={<Email />}
          placeholderTextColor={'#292D32'}
          containerStyle={{borderColor: '#ECECEC', marginTop: 16}}
          value={formik.values.email}
          onChangeText={formik.handleChange('email')}
          onBlur={formik.handleBlur('email')}
          errorMessage={formik.touched.email ? formik.errors.email : undefined}
          editable={false}
        />

        {/* MESSAGE INPUT */}
        <View style={styles.messageInputContainer}>
          <TextInput
            placeholder="Message"
            placeholderTextColor={'#292D32'}
            style={{fontFamily: FONTS.Nunito_Regular}}
            multiline
            numberOfLines={5}
            value={formik.values.message}
            onChangeText={formik.handleChange('message')}
            onBlur={formik.handleBlur('message')}
            textAlignVertical="top"
          />
        </View>
        {formik.touched.message && formik.errors.message && (
          <Text style={styles.errorText}>{formik.errors.message}</Text>
        )}

        {/* SUBMIT BUTTON */}
        <TextButton
          label={'Submit'}
          containerStyle={styles.submitButton}
          onPress={formik.handleSubmit}
          disabled={loading || !formik.isValid || !formik.dirty}
          isLoading={loading}
        />

        {/* INFO TEXT */}
        <Text style={styles.paragraph}>
          If you have any questions or need assistance, feel free to reach out.
          Our support team is here to help you with any issues or inquiries.
        </Text>
      </ScrollView>
    </View>
  );
};

export default ContactSupport;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingTop: 30,
    paddingHorizontal: 20,
  },
  messageInputContainer: {
    height: 150,
    backgroundColor: '#F8F8F8',
    marginTop: 16,
    borderRadius: 8,
    paddingHorizontal: 10,
    borderColor: '#ECECEC',
    borderWidth: 1,
    paddingTop: Platform.OS === 'android' ? 0 : 10,
  },
  submitButton: {
    height: 55,
    backgroundColor: '#EB4E1F',
    marginTop: 20,
    borderRadius: 8,
  },
  paragraph: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 24,
    textAlign: 'center',
    fontFamily: FONTS.Nunito_Regular,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 4,
    fontFamily: FONTS.Nunito_Regular,
  },
});
