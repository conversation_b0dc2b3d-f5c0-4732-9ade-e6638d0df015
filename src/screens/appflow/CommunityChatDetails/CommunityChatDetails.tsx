import {
  SafeAreaView,
  StyleSheet,
  Text,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import React, {useCallback, useEffect} from 'react';
import {
  Bubble,
  GiftedChat,
  InputToolbar,
  Send,
  Time,
} from 'react-native-gifted-chat';
import {COLORS, FONTS} from '../../../themes';
import {SendFill} from '../../../assets/svgicons';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack';
import ChatHeader from './ChatHeader';
import useCommunity from '../../../Hooks/useCommunity';
import {useAppSelector} from '../../../redux/config/Store';
type Props = NativeStackScreenProps<HomeStackParamList, 'CommunityChatDetails'>;

const CommunityChatDetails: React.FC<Props> = ({navigation, route}) => {
  const {communityId, communityLenght, topicName} = route?.params;
  const user = useAppSelector(state => state.auth.user);
  const {
    leaveCommunity,
    sendCommunityMessage,
    getCommunityMessages,
    messages,
    messageLoading,
  } = useCommunity();

  useEffect(() => {
    getCommunityMessages(communityId);
  }, []);
  const onSend = useCallback((messages = []) => {
    sendCommunityMessage(communityId, messages);
  }, []);

  return (
    // <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
    <SafeAreaView style={styles.container}>
      <ChatHeader
        topicName={topicName}
        communityLenght={communityLenght}
        onPressLeave={() => {
          leaveCommunity(communityId, user._id);
          setTimeout(() => {
            navigation.goBack();
          }, 200);
        }}
        onPressBackButton={() => navigation.goBack()}
      />
      {messageLoading ? (
        <ActivityIndicator
          size={'large'}
          style={{marginTop: Dimensions.get('screen').height / 3}}
        />
      ) : (
        <>
          <GiftedChat
            messages={messages}
            onSend={messages => onSend(messages)}
            showUserAvatar={true}
            renderUsernameOnMessage={true} // default is false
            user={{_id: user._id, name: user.name}}
            alwaysShowSend
            keyboardShouldPersistTaps={'never'}
            showAvatarForEveryMessage={false}
            listViewProps={{showsVerticalScrollIndicator: false}}
            scrollToBottom
            inverted={messages?.length !== 0}
            messagesContainerStyle={messages?.length === 0 && styles.emptyChat}
            renderChatEmpty={() => (
              <Text style={styles.heading}>No messages!</Text>
            )}
            placeholder="Write a reply..."
            textInputProps={{
              color: '#000',
              paddingRight: 56,
            }}
            renderBubble={props => (
              <Bubble
                {...props}
                wrapperStyle={{
                  right: styles.rightBubble,
                  left: styles.leftBubble,
                }}
              />
            )}
            renderInputToolbar={props => (
              <InputToolbar
                {...props}
                containerStyle={styles.inputToolbar}
                primaryStyle={styles.inputPrimary}
              />
            )}
            renderSend={props => (
              <Send
                {...props}
                disabled={!props.text}
                containerStyle={[
                  styles.sendButton,
                  !props.text && styles.disabledSend,
                ]}>
                <SendFill />
              </Send>
            )}
            renderTime={props => (
              <Time
                {...props}
                timeTextStyle={{
                  left: styles.timeLeft,
                  right: styles.timeRight,
                }}
              />
            )}
          />
        </>
      )}
    </SafeAreaView>
    // </TouchableWithoutFeedback>
  );
};

export default CommunityChatDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  sendButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
    marginBottom: 2,
  },
  disabledSend: {
    opacity: 0.5,
  },
  heading: {
    textAlign: 'center',
    fontSize: 20,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_SemiBold,
    paddingTop: 200,
  },
  emptyChat: {
    transform: [{scaleY: -1}],
  },
  inputToolbar: {
    backgroundColor: COLORS.white,
    borderWidth: 1.5,
    borderTopWidth: 2,
    borderColor: COLORS.red,
    borderTopColor: COLORS.red,
    borderRadius: 30,
    justifyContent: 'center',
    marginHorizontal: 28,
    marginBottom: 10,
  },
  inputPrimary: {
    alignItems: 'center',
  },
  rightBubble: {
    backgroundColor: COLORS.red,
    borderRadius: 0,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 0,
    padding: 5,
    marginBottom: 15,
    // marginRight: 20,
    marginLeft: 100,
  },
  leftBubble: {
    backgroundColor: '#E9E9E9',
    borderRadius: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    padding: 5,
    marginBottom: 15,
    // marginLeft: -25,
  },
  timeLeft: {
    color: 'rgba(0, 0, 0, 0.46)',
    fontSize: 10,
    textAlign: 'right',
    position: 'absolute',
    bottom: -24,
    fontFamily: FONTS.Nunito_Light,
  },
  timeRight: {
    color: 'rgba(0, 0, 0, 0.46)',
    fontSize: 10,
    textAlign: 'left',
    position: 'absolute',
    bottom: -24,
    right: 0,
    fontFamily: FONTS.Nunito_Light,
  },
});
