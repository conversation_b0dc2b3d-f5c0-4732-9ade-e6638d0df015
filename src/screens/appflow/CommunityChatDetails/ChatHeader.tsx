import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {ArrowLeft} from '../../../assets/svgicons';
import {ButtonwithIcon} from '../../../components';
import {COLORS, FONTS} from '../../../themes';

interface Props {
  topicName: string;
  communityLenght: number;
  onPressLeave: () => void;
  onPressBackButton: () => void;
}

const ChatHeader: React.FC<Props> = ({
  topicName,
  communityLenght,
  onPressLeave,
  onPressBackButton,
}) => {
  return (
    <View style={styles.container}>
      <ButtonwithIcon
        icon={<ArrowLeft fill="#000" />}
        onPress={onPressBackButton}
        containerStyle={{marginRight: 12}}
      />
      <View style={{flex: 1}}>
        <Text style={styles.name}>{topicName}</Text>
        <Text style={styles.paragraph}>
          {communityLenght} people are participating
        </Text>
      </View>
      <TouchableOpacity hitSlop={10} onPress={onPressLeave}>
        <Text style={styles.leaveText}>Leave</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  name: {
    fontSize: 14,
    color: COLORS.black,
    fontFamily: FONTS.Nunito_Medium,
  },
  paragraph: {
    fontSize: 10,
    color: '#505050',
    fontFamily: FONTS.Nunito_Medium,
    paddingTop: 2,
  },
  leaveText: {
    fontSize: 14,
    color: '#FF0000',
    fontFamily: FONTS.Nunito_Medium,
  },
});
