import {createAction, createReducer, PayloadAction} from '@reduxjs/toolkit';

export const setFcmToken = createAction<string | null>('SET_FCM_TOKEN');

type FCMState = {
  fcmToken: string | null;
};

const initialState: FCMState = {
  fcmToken: null,
};

export const fcmReducer = createReducer(initialState, builder => {
  builder.addCase(
    setFcmToken,
    (state, action: PayloadAction<string | null>) => {
      state.fcmToken = action.payload;
    },
  );
});
