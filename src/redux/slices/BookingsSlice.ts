import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { IBooking } from "../../models/IBooking";
import BookingService from "../../Services/BookingService";

// Async thunk for fetching bookings
export const fetchBookings = createAsyncThunk("bookings/fetchBookings", async (filters: any, { rejectWithValue }) => {
  try {
    const response = await BookingService.GetBookings(filters || {});
    return response.data?.results;
  } catch (error) {
    console.log("FETCH BOOKINGS THUNK >> ", error.response);
    return rejectWithValue("Error");
  }
});

// Async thunk for active fetching bookings
export const fetchActiveBookingsThunk = createAsyncThunk("bookings/fetchActiveBookingsThunk", async ({ userId, userType }: { userId: string; userType: "doctor" | "patient" }, { rejectWithValue }) => {
  try {
    let filters: Record<string, unknown> = {
      status: 'active',
    };
    if (userType === "doctor") filters.doctor = userId;
    if (userType === "patient") filters.patient = userId;
    const response = await BookingService.GetBookings(filters);
    return response.data?.results;
  } catch (error) {
    console.log("FETCH ACTIVE BOOKINGS THUNK >> ", error);
    return rejectWithValue(error?.message || "Error happened");
  }
});


// Async thunk for active fetching bookings
export const fetchCompletedBookingsThunk = createAsyncThunk("bookings/fetchCompletedBookingsThunk", async ({ userId, userType }: { userId: string; userType: "doctor" | "patient" }, { rejectWithValue }) => {
  try {
    let filters: Record<string, unknown> = {
      isPast: true,
    };
    if (userType === "doctor") filters.doctor = userId;
    if (userType === "patient") filters.patient = userId;
    const response = await BookingService.GetBookings(filters);
    return response.data?.results;
  } catch (error) {
    console.log("fetchCompletedBookingsThunk >> ", error);
    return rejectWithValue(error?.message || "Error happened");
  }
});

// Async thunk for fetching bookings
export const markBookingComplete = createAsyncThunk(
  "bookings/markComplete",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await BookingService.MarkBookingComplete(id);
      console.log("BOOKING > MARK COMPLETE", response.data);

      return response.data;
    } catch (error) {
      console.log("error of mark booking >> ", error.response.data);
      return rejectWithValue("Error");
    }
  },
);

interface BookingResult {
  results: IBooking[];
  loading: boolean;
  error: string | null;
}
interface InitialState {
  active: BookingResult;
  completed: BookingResult;
  bookingDetails: IBooking | null;
}

// Initial state
const initialState: InitialState = {
  active: { error: null, loading: false, results: [] },
  completed: { error: null, loading: false, results: [] },
  bookingDetails: null,
};

const bookingSlice = createSlice({
  name: "bookings",
  initialState,
  reducers: {
    addBookingAction: (state, { payload }: PayloadAction<IBooking>) => {
      state.active.results = [payload, ...state.active.results];
      return state;
    },
    updateBookingAction: (state, { payload }: PayloadAction<Partial<IBooking>>) => {
      const index_a = state.active.results.findIndex((item) => item._id === payload?._id);
      if (index_a !== -1) {
        state.active.results[index_a] = {
          ...state.active.results[index_a],
          patient: state.active.results[index_a].patient,
          doctor: state.active.results[index_a].doctor,
          ...payload,
        };
      }
      const index_c = state.active.results.findIndex((item) => item._id === payload?._id);
      if (index_c !== -1) {
        state.active.results[index_c] = {
          ...state.completed.results[index_c],
          patient: state.completed.results[index_c].patient,
          doctor: state.completed.results[index_c].doctor,
          ...payload,
        };
      }
      return state;
    },
    bookingResetAction: (state) => {
      state = initialState;
      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // * FETCH ACTIVE BOOKINGS
      .addCase(fetchActiveBookingsThunk.pending, (state) => {
        state.active.loading = true;
        state.active.error = null;
      })
      .addCase(fetchActiveBookingsThunk.fulfilled, (state, { payload }: PayloadAction<IBooking[]>) => {
        state.active.loading = false;
        state.active.results = payload;
      })
      .addCase(fetchActiveBookingsThunk.rejected, (state, action) => {
        state.active.loading = false;
        state.active.error = action.payload as string;
      })
      // * FETCH COMPLETED BOOKINGS
      .addCase(fetchCompletedBookingsThunk.pending, (state) => {
        state.completed.loading = true;
        state.completed.error = null;
      })
      .addCase(fetchCompletedBookingsThunk.fulfilled, (state, { payload }: PayloadAction<IBooking[]>) => {
        state.completed.loading = false;
        state.completed.results = payload;
      })
      .addCase(fetchCompletedBookingsThunk.rejected, (state, action) => {
        state.completed.loading = false;
        state.completed.error = action.payload as string;
      })
      .addCase(markBookingComplete.pending, (state) => {
        // will be needed incase of managing loading
      })
      .addCase(markBookingComplete.fulfilled, (state, { payload }: PayloadAction<IBooking>) => {
        state.active.loading = false;
        state.completed.loading = false;
        state.active.results = state.active.results.filter(b => b._id !== payload._id);
        state.completed.results = [payload, ...state.completed.results];
      });
  },
});

export const { addBookingAction, updateBookingAction, bookingResetAction } = bookingSlice.actions;

export default bookingSlice;
