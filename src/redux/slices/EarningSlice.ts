import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import EarningService from "../../Services/EarningService";
import { IEarning } from "../../models/IEarning";
import moment from "moment";

type EarningResults = IEarning[];
type EarningMeta = {
  totalEarnings: number;
  currency: string;
};

type MetaActiveBookingForEarning = {
  activeBookings: number;
  activeBookingsEarning: number;
};

interface EarningSubstate {
  results: EarningResults;
  meta: EarningMeta;
  loading: boolean;
  error: string | null;
}

// Define the slice state type
interface EarningState {
  overall: EarningSubstate;
  currentPhase: EarningSubstate;
  previousMonth: EarningSubstate;
  activeBooking: {
    meta: MetaActiveBookingForEarning;
    loading: boolean;
    error: null | string;
  };
}

// Initial state
const initialState: EarningState = {
  overall: {
    error: null,
    loading: false,
    meta: { currency: "", totalEarnings: 0 },
    results: [],
  },
  currentPhase: {
    error: null,
    loading: false,
    meta: { currency: "", totalEarnings: 0 },
    results: [],
  },
  previousMonth: {
    error: null,
    loading: false,
    meta: { currency: "", totalEarnings: 0 },
    results: [],
  },
  activeBooking: {
    error: null,
    loading: false,
    meta: { activeBookings: 0, activeBookingsEarning: 0 },
  },
};

// Async thunk for fetching logged in doctor earnings
export const fetchOverallEarnings = createAsyncThunk(
  "earnings/fetchOverallEarnings",
  async (doctorId: string, { rejectWithValue }) => {
    try {
      const { data: overall_earnings } = await EarningService.GetEarningByDoctor(doctorId, {});
      const { data: results, meta }: { data: EarningResults; meta: EarningMeta } = overall_earnings;
      console.log("overall_earnings >>", overall_earnings);

      return {
        results,
        meta,
      };
    } catch (error) {
      console.log("fetchEarning >> ", error.response.data);
      return rejectWithValue("Error while fetching earnings");
    }
  },
);

// Async thunk for fetching logged in doctor earnings
export const fetchCurrentPhaseEarnings = createAsyncThunk(
  "earnings/fetchCurrentPhaseEarnings",
  async (
    { doctorId, startDate, endDate }: { doctorId: string; startDate: string; endDate: string },
    { rejectWithValue },
  ) => {
    const params = {
      startDate,
      endDate,
    };

    try {
      const { data: current_month_earnings } = await EarningService.GetEarningByDoctor(doctorId, params);
      const { data: results, meta }: { data: EarningResults; meta: EarningMeta } = current_month_earnings;
      return {
        results,
        meta,
      };
    } catch (error) {
      console.log("fetchCurrentPhaseEarnings >> ", error.response.data);
      return rejectWithValue("Error while fetching earnings");
    }
  },
);

// Async thunk for fetching logged in doctor earnings
export const fetchPreviousMonthEarnings = createAsyncThunk(
  "earnings/fetchPreviousMonthEarnings",
  async (doctorId: string, { rejectWithValue }) => {
    const params = {
      startDate: moment().subtract(1, "month").startOf("month").format("YYYY-MM-DD"),
      endDate: moment().subtract(1, "month").endOf("month").format("YYYY-MM-DD"),
    };

    try {
      const { data: previous_month_earnings } = await EarningService.GetEarningByDoctor(doctorId, params);
      const { data: results, meta } = previous_month_earnings;
      return { results, meta };
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Unknown error";
      return rejectWithValue({
        message: "Error while fetching earnings",
        details: errorMessage,
      });
    }
  },
);

// Async thunk for fetching active booking in earnings
export const fetchActiveBookingsForEarning = createAsyncThunk(
  "earnings/fetchActiveBookingsForEarning",
  async (doctorId: string, { rejectWithValue }) => {
    try {
      const { data: meta_active_bookings } = await EarningService.GetActiveBookingsForEarning(doctorId);
      return meta_active_bookings;
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Unknown error";
      return rejectWithValue({
        message: "Error while fetching active booking in earnings",
        details: errorMessage,
      });
    }
  },
);

const earningSlice = createSlice({
  name: "earnings",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // FETCH OVERALL EARNINGS
      .addCase(fetchOverallEarnings.pending, (state) => {
        state.overall.loading = true;
        state.overall.error = null;
      })
      .addCase(fetchOverallEarnings.fulfilled, (state, action) => {
        state.overall.loading = false;
        state.overall.results = action.payload.results;
        state.overall.meta = action.payload.meta;
      })
      .addCase(fetchOverallEarnings.rejected, (state, action) => {
        state.overall.loading = false;
        state.overall.error = action.payload as string;
      })
      // FETCH CURRENT MONTH EARNINGS
      .addCase(fetchCurrentPhaseEarnings.pending, (state) => {
        state.currentPhase.loading = true;
        state.currentPhase.error = null;
      })
      .addCase(fetchCurrentPhaseEarnings.fulfilled, (state, action) => {
        state.currentPhase.loading = false;
        state.currentPhase.results = action.payload.results;
        state.currentPhase.meta = action.payload.meta;
      })
      .addCase(fetchCurrentPhaseEarnings.rejected, (state, action) => {
        state.currentPhase.loading = false;
        state.currentPhase.error = action.payload as string;
      })
      // FETCH PREVOUS MONTH EARNINGS
      .addCase(fetchPreviousMonthEarnings.pending, (state) => {
        state.previousMonth.loading = true;
        state.previousMonth.error = null;
      })
      .addCase(fetchPreviousMonthEarnings.fulfilled, (state, action) => {
        state.previousMonth.loading = false;
        state.previousMonth.results = action.payload.results;
        state.previousMonth.meta = action.payload.meta;
      })
      .addCase(fetchPreviousMonthEarnings.rejected, (state, action) => {
        state.previousMonth.loading = false;
        state.previousMonth.error = (action.payload as string) || "Unknown error";
      })
      // FETCH ACTIVE BOOKINGS FOR EARNING
      .addCase(fetchActiveBookingsForEarning.pending, (state) => {
        state.activeBooking.loading = true;
        state.activeBooking.error = null;
      })
      .addCase(fetchActiveBookingsForEarning.fulfilled, (state, action) => {
        state.activeBooking.loading = false;
        state.activeBooking.meta = action.payload.meta;
      })
      .addCase(fetchActiveBookingsForEarning.rejected, (state, action) => {
        state.activeBooking.loading = false;
        state.activeBooking.error = (action.payload as string) || "Unknown error";
      });
  },
});

export const { } = earningSlice.actions;

export default earningSlice.reducer;
