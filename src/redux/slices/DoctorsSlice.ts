import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { IUser } from "../../models";
import UserService from "../../Services/UserService";

// Async thunk for fetching doctors
export const fetchDoctors = createAsyncThunk('doctors/fetchDoctors', async (filters: any, { rejectWithValue }) => {
  try {
    const response = await UserService.GetUsers(filters || {});
    return response.data?.results;
  } catch (error) {
    return rejectWithValue("Error");
  }
});

// Define the slice state type
interface DoctorState {
  results: IUser[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: DoctorState = {
  results: [],
  loading: false,
  error: null,
};

const doctorSlice = createSlice({
  name: 'doctors',
  initialState,
  reducers: {
    addDoctorAction: (state, { payload }: PayloadAction<IUser>) => {
      state.results = [
        ...state.results,
        payload
      ];
      return state;
    },
    updateDoctorAction: (state, { payload }: PayloadAction<Partial<IUser>>) => {
      const index = state.results.findIndex(item => item.id === payload?.id);
      state.results[index] = {
        ...state.results[index],
        ...payload
      };
      return state;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDoctors.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDoctors.fulfilled, (state, action) => {
        state.loading = false;
        state.results = action.payload;
      })
      .addCase(fetchDoctors.rejected, (state, action) => {
        state.loading = false;
        state.error = "Error";
      })
  }
});

export const { addDoctorAction, updateDoctorAction } = doctorSlice.actions;

export default doctorSlice;