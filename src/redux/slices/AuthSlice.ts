import { createAction, createAsyncThunk, createReducer, PayloadAction } from "@reduxjs/toolkit";
import { IUser } from "../../models/User";
import AuthServices from "../../Services/AuthServices";

interface IChooseRole {
  selectedRole: string;
  hasSelectedRole: boolean;
}

type State = {
  isLoadingSession: boolean;
  user?: IUser;
  userId: string;
  chooseRole: IChooseRole;
  isOnBoarded: boolean;
};

const initalAuthState: State = {
  isLoadingSession: false,
  user: undefined,
  userId: "",
  chooseRole: { selectedRole: "", hasSelectedRole: false },
  isOnBoarded: false,
};

export const setUserAction = createAction<IUser | undefined>("SET_USER");
export const setUserId = createAction<string | undefined>("SET_USERID");
export const setLogoutAction = createAction("LOGOUT");
export const updateUser = createAction<Partial<IUser>>("UPDATE_USER");
export const setHasSelectedRole = createAction<IChooseRole>("SET_HAS_SELECTED_ROLE");
export const setOnBoarded = createAction<boolean>("SET_ONBOARDED");

export const getLoggedInUser = createAsyncThunk("auth/getLoggedInUser", async (userId: string, { rejectWithValue }) => {
  try {
    const response = await AuthServices.GetUserById(userId);
    return response.data as IUser;
  } catch (error) {
    console.log(error);
    rejectWithValue(error);
  }
});

const reducer = createReducer(initalAuthState, (builder) => {
  builder
    .addCase(setUserAction, (state, action: PayloadAction<IUser | undefined>) => {
      state.user = action.payload;
    })
    // .addCase(setLogoutAction, () => initalAuthState)
    .addCase(setLogoutAction, (state, action) => ({
      ...state,
      user: undefined,
      userId: "",
    }))
    .addCase(setUserId, (state, action: PayloadAction<string | undefined>) => {
      state.userId = action.payload || "";
    })
    .addCase(updateUser, (state, action: PayloadAction<Partial<IUser>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    })
    .addCase(getLoggedInUser.pending, (state) => {
      state.isLoadingSession = true;
    })
    .addCase(getLoggedInUser.rejected, (state) => {
      state.isLoadingSession = false;
    })
    .addCase(getLoggedInUser.fulfilled, (state, { payload }: PayloadAction<IUser>) => {
      state.isLoadingSession = false;
      state.user = payload;
    })
    .addCase(setHasSelectedRole, (state, action: PayloadAction<IChooseRole>) => {
      state.chooseRole = action.payload;
    })
    .addCase(setOnBoarded, (state, action: PayloadAction<boolean>) => {
      state.isOnBoarded = action.payload;
    });
});

export const authReducer = reducer;
