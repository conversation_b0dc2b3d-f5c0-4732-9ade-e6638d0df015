import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { IOfferService } from "../../models/IOfferService";

// Define the slice state type
interface OfferServiceState {
  results: IOfferService[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: OfferServiceState = {
  results: [],
  loading: false,
  error: null,
};

const OfferServicesSlice = createSlice({
  name: 'offerServices',
  initialState,
  reducers: {
    setOfferServicesAction: (state, { payload }: PayloadAction<IOfferService[]>) => {
      state.results = payload;
      return state;
    },
    addOfferServiceAction: (state, { payload }: PayloadAction<IOfferService>) => {
      state.results = [
        ...state.results,
        payload
      ];
      return state;
    },
    updateOfferServicesAction: (state, { payload }) => {

    },
    resetOfferServicesAction: (state) => {
      state = initialState;
      return state;
    }
  },
});

export const { addOfferServiceAction, updateOfferServicesAction, resetOfferServicesAction, setOfferServicesAction } = OfferServicesSlice.actions;

export default OfferServicesSlice;