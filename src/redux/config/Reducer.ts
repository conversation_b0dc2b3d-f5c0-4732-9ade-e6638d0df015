import { combineReducers } from "@reduxjs/toolkit";
import { authReducer } from "../slices/AuthSlice";
import bookingSlice from "../slices/BookingsSlice";
import OfferServicesSlice from "../slices/OfferServicesSlice";
import doctorSlice from "../slices/DoctorsSlice";
import { fcmReducer } from "../slices/fcmTokenSlice";
import earningReducer from "../slices/EarningSlice";

export const appReducer = combineReducers({
  auth: authReducer,
  bookings: bookingSlice.reducer,
  offerServices: OfferServicesSlice.reducer,
  doctors: doctorSlice.reducer,
  fcmTokenSlice: fcmReducer,
  earnings: earningReducer,
});
