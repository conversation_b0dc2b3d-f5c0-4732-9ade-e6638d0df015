import { persistReducer, persistStore } from "redux-persist";
import { appReducer } from "./Reducer";
import { reduxStorage } from "./Storage";
import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector as useReduxSelector, TypedUseSelectorHook } from "react-redux";
const persistedReducer = persistReducer(
  {
    key: "root",
    blacklist: ["config"],
    storage: reduxStorage,
  },
  appReducer,
);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useReduxSelector;

export const persistor = persistStore(store);
