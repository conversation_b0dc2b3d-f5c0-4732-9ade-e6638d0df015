import { EditBookingScreenParams } from "./IBooking";

export interface IPaymentMethod {
  createdAt: number;
  id: string;
  name: string;
  slug: string;
  updatedAt: number;
}

export interface HomeCheckOutScreenParams extends EditBookingScreenParams {
  paymentMethodType: string;
}

export interface ICreatePaymentIntentPayload {
  amount: number; // in cents
  currency: "usd";
  customer_id: string;
  stripeAccountId: string;
}
