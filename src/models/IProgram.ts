export interface IExercise {
  id: string;
  duration: number;
  thumbnail: string;
  type: string;
  videoUrl: string;
  exerciseName: string;
}

export interface IProgram {
  id?: string;
  createdAt: Date;
  duration: number;
  exercises: string[];
  name: string;
  thumbnail: string;
  type: string;
  subscriber: string[];
  percentage?: number;
  completedExercises?: string[];
}

export interface IUserProgram {
  id: string;
  userId: string;
  programId: string;
  completedExercises: string[];
}
export interface ITreatmentVideos {
  id?: string;
  duration: number;
  thumbnail: string;
  type: string;
  videoUrl: string;
  name: string;
}
