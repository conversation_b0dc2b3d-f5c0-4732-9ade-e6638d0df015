import { Moment } from "moment";
import { IUser } from ".";
import { IOfferService } from "./IOfferService";

export interface IBookingDateTime {
  date: string;
  day: string;
  time: string;
}

export interface EditBookingScreenParams {
  bookingDateTime: IBookingDateTime;
  service: IOfferService;
  doctor: IUser;
  selectedAppointment: IOfferService;
}

export interface IBooking extends EditBookingScreenParams {
  _id: string;
  paymentMethod: string;
  status: "active" | "cancelled" | "pending" | "completed";
  patient: IUser;
  review?: {
    rating: number;
    review: string;
  };
  createdAt: string;
  updatedAt: string;
  payment: {
    payment_id: string;
    paidAt: string;
    amount: number;
  };
}
