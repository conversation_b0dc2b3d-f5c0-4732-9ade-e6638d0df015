import { IOfferService } from "./IOfferService";

export interface IDaySchedule {
  day: string | "sunday" | "monday" | "tuesday" | "wednesday" | "thurday" | "friday" | "saturday";
  from: string;
  to: string;
}

export interface IUser {
  _id: string;
  name: string;
  email: string;
  phoneNumber: string;
  isOnboarded: boolean;
  address?: string;
  stripeCustomerId?: string;
  stripeAccountId?: string;
  isStripeConnected?: boolean;
  isVerified: boolean;
  userType: "doctor" | "patient";
  avgRating: number;
  createdAt: number;
  eligibilityConfirmation: {
    degree: string;
    license: string;
  };
  gender: "male" | "female";
  introduction?: string;
  photo: string;
  schedule: IDaySchedule[];
  services: IOfferService[];
  status: "pending";
  subscribedAt: number;
  subscription: any;
  subscriptionStatus: "none";
  updatedAt: number;
  verificationCode: string;
  workExperience: {
    patients: string;
    specialization: string[];
    years: string;
  };
  about?: string;
  receiveNotifications?: boolean;
}

export interface UploadedImageResponse {
  url: string;
  imageName: string;
}
