import {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';

export enum MessageType {
  TEXT = 'text',
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
}

export interface IMessage {
  id: string;
  type: MessageType[];
  text: string;
  video?: string;
  audio?: string;
  image?: string;
  chatId: string;
  sender: string;
  isRead: boolean;
  createdAt: any;
}

export interface IMessageGifted extends IMessage {
  _id: string;
  user: any;
}

export interface IChat {
  id: string;
  currentMembers: string[];
  lastMessage: null | IMessage;
  unreadCount: number;
  initializedBy: string;
  createdAt: any;
  updatedAt: any;
}
