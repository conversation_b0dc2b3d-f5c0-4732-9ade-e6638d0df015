import { IBooking } from "./IBooking";

export interface IEarning {
  _id: string;
  booking: IBooking;
  earnedAt: string;
  paymentId: string;
  status: string;
  service: {
    name: string;
    sessionTime: number; // in minutes
    price: number; // in cents
  };
  createdAt: string;
  updatedAt: string;
}

export interface IAccountBalance {
  available: [
    {
      amount: number;
      currency: "usd";
    }
  ],
  pending: [
    {
      amount: number;
      currency: "usd";
    }
  ]
}