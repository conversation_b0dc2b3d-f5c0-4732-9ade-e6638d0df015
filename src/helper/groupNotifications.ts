import moment from 'moment';
import {INotification} from '../models/INotification';

export const groupNotifications = (notifications: INotification[]) => {
  const today = moment().startOf('day');
  const yesterday = moment().subtract(1, 'day').startOf('day');
  const startOfWeek = moment().startOf('week');
  const startOfMonth = moment().startOf('month');

  const sections = {
    Today: [] as INotification[],
    Yesterday: [] as INotification[],
    'This Week': [] as INotification[],
    'This Month': [] as INotification[],
    Older: [] as INotification[],
  };

  notifications.forEach(notification => {
    const notificationDate = moment(notification.createdAt);
    if (notificationDate.isSame(today, 'day')) {
      sections.Today.push(notification);
    } else if (notificationDate.isSame(yesterday, 'day')) {
      sections.Yesterday.push(notification);
    } else if (notificationDate.isAfter(startOfWeek)) {
      sections['This Week'].push(notification);
    } else if (notificationDate.isAfter(startOfMonth)) {
      sections['This Month'].push(notification);
    } else {
      sections.Older.push(notification);
    }
  });

  return Object.entries(sections)
    .filter(([, data]) => data.length > 0) // Remove empty sections
    .map(([title, data]) => ({title, data}));
};
