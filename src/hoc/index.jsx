import messaging from "@react-native-firebase/messaging";
import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../redux/config/Store";
import { setFcmToken } from "../redux/slices/fcmTokenSlice";
import { Platform } from "react-native";
import Toast from "react-native-toast-message";

export const DeeplinkHandlerHOC = (RootNavigator) => {
  const MessageHandlerComponent = () => {
    const dispatch = useAppDispatch();
    const { user } = useAppSelector((state) => state.auth);

    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();
        console.log("FCM Token:", fcmToken);
        if (fcmToken) {
          dispatch(setFcmToken(fcmToken));
        }
      } catch (error) {
        console.log("errror in fcmtoken", error);
      }
    };

    const notificationListener = async () => {
      messaging().onNotificationOpenedApp((remoteMessage) => {
        if (remoteMessage) {
          console.log("Remote Notificaiton Listener", remoteMessage.notification);
        }
      });
      messaging()
        .getInitialNotification()
        .then((remoteMessage) => {
          if (remoteMessage) {
            console.log("Notification caused app to open from quit state:", remoteMessage.notification);
          }
        });

      messaging().onMessage(async (remoteMessage) => {
        console.log("notification on foreground state....", remoteMessage);

        // Only show toast if user has notifications enabled
        if (user?.receiveNotifications) {
          Toast.show({
            type: "info",
            text1: remoteMessage.notification?.title || "New Notification",
            text2: remoteMessage.notification?.body || "",
            position: "top",
            visibilityTime: 4000,
            autoHide: true,
            topOffset: Platform.OS === "ios" ? 50 : 0,
          });
        }
      });
    };

    const requestUserPermission = async () => {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        // console.log('Authorization status:', authStatus);
        getFCMToken();
      }
    };

    useEffect(() => {
      requestUserPermission();
      notificationListener();
    }, []);

    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
