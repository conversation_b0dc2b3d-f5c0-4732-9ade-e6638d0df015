import { StyleSheet } from "react-native";
import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import AuthStackNavigator from "./AuthStack";
import HomeStackNavigator from "./HomeStack";
import ProfessionalStackNavigator from "./ProfessionalStack";
import { useAppDispatch, useAppSelector } from "../redux/config/Store";
import { DeeplinkHandlerHOC } from "../hoc";
import { getLoggedInUser } from "../redux/slices/AuthSlice";
import { fetchActiveBookingsThunk, fetchCompletedBookingsThunk } from "../redux/slices/BookingsSlice";

const Navigation = () => {
  const { userId, user } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (userId) {
      dispatch(getLoggedInUser(userId));
    }
  }, [userId]);

  return (
    <NavigationContainer>
      {!userId ? <AuthStackNavigator /> : null}
      {userId && user?.userType === "patient" ? <HomeStackNavigator /> : null}
      {userId && user?.userType === "doctor" ? <ProfessionalStackNavigator /> : null}
    </NavigationContainer>
  );
};

export default DeeplinkHandlerHOC(Navigation);

const styles = StyleSheet.create({});
