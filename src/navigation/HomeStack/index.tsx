import { StyleSheet, View } from "react-native";
import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import BottomTabNavigator from "../BottomTab";
import {
  AboutUs,
  ContactSupport,
  Notification,
  PrivacyPolicy,
  Profile,
  Settings,
  TermsAndConditions,
  Chats,
  ChatDetails,
  Subscription,
  MyProgram,
  ProgramDetails,
  PatientAgendaView,
  BookingList,
  BookingDetails,
  Session,
  DoctorDetails,
  EditBookingDetails,
  Blogs,
  VideoBlogs,
  ChooseDoctor,
  HomeCheckOut,
  HomePaymentMethod,
  EditPatientProfile,
  EditPatientInfo,
  MediaPlayer,
  BlogsDetails,
  TreatmentVideos,
  CommunityList,
  CommunityChatDetails,
} from "../../screens/appflow";

import { ButtonwithIcon } from "../../components";
import { ArrowLeft, ArrowLeft2, Calendar4, Cross } from "../../assets/svgicons";
import { COLORS, FONTS } from "../../themes";
import Language from "../../screens/appflow/Language";
import { useNavigation } from "@react-navigation/native";
import { IUser } from "../../models";
import { EditBookingScreenParams } from "../../models/IBooking";
import { HomeCheckOutScreenParams } from "../../models/IPayment";
import { ProfileVerificationStep1 } from "../../screens/authflow";
import { IBlogs } from "../../models/IBlog";

export type BottomStackParamList = {
  Home: undefined;
  TalktoDoctor: undefined;
  VideoArena: undefined;
};
export type HomeStackParamList = {
  bottom: BottomStackParamList;
  Home: undefined;
  TalkToDoctor: undefined;
  VideoArena: undefined;
  Notification: undefined;
  Profile: undefined;
  TermsAndConditions: undefined;
  PrivacyPolicy: undefined;
  AboutUs: undefined;
  Settings: undefined;
  Language: undefined;
  ContactSupport: undefined;
  CommunityList: undefined;
  CommunityChatDetails: {
    communityId: string;
    communityLenght: number;
    topicName: string;
  };
  Chats: undefined;
  ChatDetails: { otherUser?: IUser };
  Subscription: undefined;
  ProgramDetails: { programId: string; isAssigned?: boolean };
  MyProgram: undefined;
  BookingList: undefined;
  BookingDetails: { bookingId: string };
  Session: { sessionId: string; doctorId: string };
  DoctorDetails: { doctor: IUser };
  EditBookingDetails: { bookingData: EditBookingScreenParams; shouldGoBack?: boolean };
  Blogs: undefined;
  VideoBlogs: undefined;
  ChooseDoctor: { pain: string };
  HomeCheckOut: { checkoutData: HomeCheckOutScreenParams };
  HomePaymentMethod: { bookingData: EditBookingScreenParams };
  PatientAgendaView: undefined;
  EditPatientProfile: undefined;
  UpdateProfilePicture: { userId: string; isProfile?: boolean };
  EditPatientInfo: undefined;
  MediaPlayer: { videoUrl: string; programId: string; exerciseId: string; isAssigned?: boolean };
  BlogsDetails: { blog: IBlogs };
  TreatmentVideos: undefined;
};
const HomeStack = createNativeStackNavigator<HomeStackParamList>();

const HomeStackNavigator = () => {
  const { goBack, navigate } = useNavigation();
  return (
    <HomeStack.Navigator
      screenOptions={({ navigation }) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleStyle: {
          fontSize: 16,
          fontFamily: FONTS.Nunito_Bold,
          color: COLORS.black,
        },
        headerLeft: () => (
          <ButtonwithIcon
            icon={<ArrowLeft fill="#000" />}
            containerStyle={{ marginRight: 16 }}
            onPress={() => navigation.goBack()}
          />
        ),
      })}
    >
      <HomeStack.Screen name="bottom" component={BottomTabNavigator} />
      <HomeStack.Screen
        name={"Notification"}
        component={Notification}
        options={{
          headerShown: true,
          headerLeft: () => <ButtonwithIcon icon={<ArrowLeft2 />} onPress={() => goBack()} />,
          headerRight: () => <ButtonwithIcon icon={<Cross />} onPress={() => goBack()} />,
          headerTitleAlign: "center",
          headerTitle: "Notifications",
          headerTitleStyle: {
            color: COLORS.white,
          },
          headerStyle: {
            backgroundColor: COLORS.red,
          },
        }}
      />
      <HomeStack.Screen
        name={"Profile"}
        component={Profile}
        options={{
          headerShown: true,
          headerRight: () => <ButtonwithIcon icon={<Cross />} onPress={() => goBack()} />,
          headerLeft: () => <View></View>,
          headerTitle: "Profile",
          headerTintColor: COLORS.white,
          headerStyle: {
            backgroundColor: COLORS.red,
          },
        }}
      />
      <HomeStack.Screen
        name={"TermsAndConditions"}
        component={TermsAndConditions}
        options={{
          headerShown: true,
          headerTitle: "Terms & Conditions",
        }}
      />
      <HomeStack.Screen
        name={"PrivacyPolicy"}
        component={PrivacyPolicy}
        options={{
          headerShown: true,
          headerTitle: "Privacy Policy",
        }}
      />
      <HomeStack.Screen
        name={"AboutUs"}
        component={AboutUs}
        options={{
          headerShown: true,
          headerTitle: "About Us",
        }}
      />
      <HomeStack.Screen
        name={"Settings"}
        component={Settings}
        options={{
          headerShown: true,
          headerTitle: "Settings",
        }}
      />
      <HomeStack.Screen
        name={"Language"}
        component={Language}
        options={{
          headerShown: true,
          headerTitle: "Language",
        }}
      />
      <HomeStack.Screen
        name={"ContactSupport"}
        component={ContactSupport}
        options={{
          headerShown: true,
          headerTitle: "Contact Support",
        }}
      />
      <HomeStack.Screen
        name={"CommunityList"}
        component={CommunityList}
        options={{
          headerShown: true,
          headerTitle: "Community",
        }}
      />
      <HomeStack.Screen
        name={"CommunityChatDetails"}
        component={CommunityChatDetails}
        options={{ headerShown: false }}
      />

      <HomeStack.Screen name={"ChatDetails"} component={ChatDetails} options={{ headerShown: false }} />
      <HomeStack.Screen name={"Chats"} component={Chats} options={{ headerShown: false }} />
      <HomeStack.Screen
        name={"Subscription"}
        component={Subscription}
        options={{
          headerShown: true,
          headerTitle: "Subscription",
        }}
      />
      <HomeStack.Screen
        name={"MyProgram"}
        component={MyProgram}
        options={{
          headerShown: true,
          headerTitle: "My Programs",
        }}
      />
      <HomeStack.Screen
        name={"ProgramDetails"}
        component={ProgramDetails}
        options={{
          headerShown: true,
          headerTitle: "Program Details",
        }}
      />
      <HomeStack.Screen
        name={"PatientAgendaView"}
        component={PatientAgendaView}
        options={{
          headerShown: true,
          headerTitle: "Your Bookings Calendar",
        }}
      />
      <HomeStack.Screen
        name={"BookingList"}
        component={BookingList}
        options={{
          headerShown: true,
          headerTitle: "Bookings",
        }}
      />
      <HomeStack.Screen name={"BookingDetails"} component={BookingDetails} options={{ headerShown: true }} />
      <HomeStack.Screen
        name={"Session"}
        component={Session}
        options={{
          headerShown: false,
        }}
      />
      <HomeStack.Screen name={"DoctorDetails"} component={DoctorDetails} options={{ headerShown: false }} />
      <HomeStack.Screen
        name={"EditBookingDetails"}
        component={EditBookingDetails}
        options={{
          headerShown: true,
          headerTitle: "Edit Your Appointment",
        }}
      />

      <HomeStack.Screen
        name={"Blogs"}
        component={Blogs}
        options={{
          headerShown: true,
          headerTitle: "Blogs",
        }}
      />
      <HomeStack.Screen
        name={"VideoBlogs"}
        component={VideoBlogs}
        options={{
          headerShown: true,
          headerTitle: "Treatment Videos",
        }}
      />
      <HomeStack.Screen
        name={"ChooseDoctor"}
        component={ChooseDoctor}
        options={{
          headerShown: true,
          headerTitleAlign: "left",
          headerTitle: "Home",
        }}
      />
      <HomeStack.Screen
        name={"HomeCheckOut"}
        component={HomeCheckOut}
        options={{
          headerShown: true,
          headerTitle: "Payment Methods",
        }}
      />
      <HomeStack.Screen
        name={"HomePaymentMethod"}
        component={HomePaymentMethod}
        options={{
          headerShown: true,
          headerTitle: "Booking Payment",
        }}
      />
      <HomeStack.Screen
        name={"EditPatientProfile"}
        component={EditPatientProfile}
        options={{
          headerShown: false,
          headerTitle: "Edit Profile",
        }}
      />
      <HomeStack.Screen
        name="UpdateProfilePicture"
        component={ProfileVerificationStep1}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Profile Image",
        }}
      />
      <HomeStack.Screen
        name="EditPatientInfo"
        component={EditPatientInfo}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Edit Info",
        }}
      />
      <HomeStack.Screen
        name="MediaPlayer"
        component={MediaPlayer}
        options={{
          headerShown: false,
        }}
      />

      <HomeStack.Screen
        name="BlogsDetails"
        component={BlogsDetails}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Blog Details",
        }}
      />
      <HomeStack.Screen
        name="TreatmentVideos"
        component={TreatmentVideos}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Treatment Videos",
        }}
      />
    </HomeStack.Navigator>
  );
};

export default HomeStackNavigator;

const styles = StyleSheet.create({});
