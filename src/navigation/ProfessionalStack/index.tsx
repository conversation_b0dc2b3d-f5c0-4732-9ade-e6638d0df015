import React from "react";
import { TouchableOpacity } from "react-native";
import { createNativeStackNavigator, NativeStackNavigationOptions } from "@react-navigation/native-stack";
import ProfessionalBottomTab from "../ProfessionalBottomTab";
import {
  AboutUs,
  ChatDetails,
  CommunityList,
  PrivacyPolicy,
  Settings,
  TermsAndConditions,
  Chats,
  EditDoctorDetails,
  ProfessionalAgendaView,
  ProfessionalBookingDetails,
  Session,
  EarningsDetails,
  EditDoctorInfo,
  ContactSupport,
  CommunityChatDetails,
  Notification,
  ConnectedAccount,
} from "../../screens/appflow";
import { ArrowLeft, Calendar4 } from "../../assets/svgicons";
import { COLORS, FONTS } from "../../themes";
import {
  ProfileVerificationStep1,
  ProfileVerificationStep2,
  ProfileVerificationStep5,
  ProfileVerificationStep6,
} from "../../screens/authflow";
import { IUser } from "../../models";

// Define type for the stack parameters
export type ProfessionalStackParamList = {
  professionaltab: undefined;
  Settings: undefined;
  TermsAndConditions: undefined;
  PrivacyPolicy: undefined;
  AboutUs: undefined;
  ChatDetails: { otherUser?: IUser };
  Chats: undefined;
  EditDoctorDetails: undefined;
  ProfessionalAgendaView: undefined;
  ProfessionalBookingDetails: { bookingId: string };
  Session: { sessionId: string; doctorId: string };
  EarningsDetails: undefined;
  EditIntroduction: { userId: string; isProfile?: boolean };
  UpdateProfilePicture: { userId: string; isProfile?: boolean };
  AddNewService: { userId: string; isProfile?: boolean };
  UpdateServicesList: { userId: string; isProfile?: boolean };
  EditDoctorInfo: undefined;
  ContactSupport: undefined;
  ConnectedAccount: undefined;
  CommunityList: undefined;
  CommunityChatDetails: {
    communityId: string;
    communityLenght: number;
    topicName: string;
  };
  Notification: undefined;
};

const ProfessionalStack = createNativeStackNavigator<ProfessionalStackParamList>();

const ProfessionalStackNavigator: React.FC = () => {
  return (
    <ProfessionalStack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: false,
        headerShadowVisible: false,
        headerLeft: () => (
          <TouchableOpacity style={{ width: 30, marginLeft: 4 }} onPress={() => navigation.goBack()} hitSlop={10}>
            <ArrowLeft fill="#000" />
          </TouchableOpacity>
        ),
        headerTitleStyle: {
          fontSize: 16,
          color: COLORS.black,
          fontFamily: FONTS.Nunito_Bold,
        },
      })}
    >
      <ProfessionalStack.Screen
        name="professionaltab"
        component={ProfessionalBottomTab}
        options={{ headerShown: false }}
      />

      <ProfessionalStack.Screen
        name="Settings"
        component={Settings}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Setting",
        }}
      />

      <ProfessionalStack.Screen
        name="TermsAndConditions"
        component={TermsAndConditions}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Terms & Conditions",
        }}
      />

      <ProfessionalStack.Screen
        name="PrivacyPolicy"
        component={PrivacyPolicy}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Privacy Policy",
        }}
      />

      <ProfessionalStack.Screen
        name="AboutUs"
        component={AboutUs}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "About Us",
        }}
      />

      <ProfessionalStack.Screen
        name="ConnectedAccount"
        component={ConnectedAccount}
        options={{
          headerShown: false,
          headerTitleAlign: "center",
          headerTitle: "Payment Profile",
        }}
      />
      <ProfessionalStack.Screen
        name="CommunityList"
        component={CommunityList}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Community",
        }}
      />
      <ProfessionalStack.Screen
        name="CommunityChatDetails"
        component={CommunityChatDetails}
        options={{ headerShown: false }}
      />

      <ProfessionalStack.Screen name="ChatDetails" component={ChatDetails} options={{ headerShown: false }} />

      <ProfessionalStack.Screen name="Chats" component={Chats} options={{ headerShown: false }} />

      <ProfessionalStack.Screen
        name="EditDoctorDetails"
        component={EditDoctorDetails}
        options={{ headerShown: false }}
      />

      <ProfessionalStack.Screen
        name="ProfessionalAgendaView"
        component={ProfessionalAgendaView}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Your Bookings Calendar",
        }}
      />

      <ProfessionalStack.Screen
        name="EditIntroduction"
        component={ProfileVerificationStep2}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "About",
        }}
      />

      <ProfessionalStack.Screen
        name="UpdateProfilePicture"
        component={ProfileVerificationStep1}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Profile Image",
        }}
      />

      <ProfessionalStack.Screen
        name="AddNewService"
        component={ProfileVerificationStep5}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Add New Service",
        }}
      />

      <ProfessionalStack.Screen
        name="UpdateServicesList"
        component={ProfileVerificationStep6}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Update Services",
        }}
      />

      <ProfessionalStack.Screen
        name="EditDoctorInfo"
        component={EditDoctorInfo}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Edit Info",
        }}
      />

      <ProfessionalStack.Screen
        name="ProfessionalBookingDetails"
        component={ProfessionalBookingDetails}
        options={({ route, navigation }) => ({
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Booking Details",
        })}
      />

      <ProfessionalStack.Screen name="Session" component={Session} />

      <ProfessionalStack.Screen
        name="EarningsDetails"
        component={EarningsDetails}
        options={{
          headerShown: true,
          headerTitleAlign: "center",
          headerTitle: "Earnings Detail",
          headerShadowVisible: false,
        }}
      />
      <ProfessionalStack.Screen
        name={"ContactSupport"}
        component={ContactSupport}
        options={{
          headerShown: true,
          headerTitle: "Contact Support",
        }}
      />
      <ProfessionalStack.Screen
        name={"Notification"}
        component={Notification}
        options={{
          headerShown: true,
          headerTitle: "Notifications",
        }}
      />
    </ProfessionalStack.Navigator>
  );
};

export default ProfessionalStackNavigator;
